-- Enhance corporate_accounts table with additional fields
ALTER TABLE corporate_accounts 
ADD COLUMN IF NOT EXISTS contact_person TEXT,
ADD COLUMN IF NOT EXISTS contact_phone TEXT,
ADD COLUMN IF NOT EXISTS business_type TEXT,
ADD COLUMN IF NOT EXISTS registration_number TEXT,
ADD COLUMN IF NOT EXISTS tax_identification_number TEXT,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS industry TEXT,
ADD COLUMN IF NOT EXISTS company_size TEXT CHECK (company_size IN ('1-10', '11-50', '51-200', '201-500', '500+')),
ADD COLUMN IF NOT EXISTS years_in_business INTEGER,
ADD COLUMN IF NOT EXISTS annual_revenue DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS bank_name TEXT,
ADD COLUMN IF NOT EXISTS account_number TEXT,
ADD COLUMN IF NOT EXISTS account_name TEXT;

-- Add indexes for better performance on new fields
CREATE INDEX IF NOT EXISTS idx_corporate_accounts_approval_status ON corporate_accounts(approval_status);
CREATE INDEX IF NOT EXISTS idx_corporate_accounts_verification_status ON corporate_accounts(verification_status);
CREATE INDEX IF NOT EXISTS idx_corporate_accounts_business_type ON corporate_accounts(business_type);
CREATE INDEX IF NOT EXISTS idx_corporate_accounts_industry ON corporate_accounts(industry);

-- Update the updated_at trigger to include new columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Recreate the trigger for corporate_accounts
DROP TRIGGER IF EXISTS update_corporate_accounts_updated_at ON corporate_accounts;
CREATE TRIGGER update_corporate_accounts_updated_at 
  BEFORE UPDATE ON corporate_accounts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies for new fields (same as existing ones, but ensuring they're applied)
-- These should already exist from the initial migration, but ensuring they're in place
DO $$
BEGIN
  -- Check if policies exist, if not create them
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'corporate_accounts' 
    AND policyname = 'Users can view own corporate account'
  ) THEN
    CREATE POLICY "Users can view own corporate account" ON corporate_accounts FOR SELECT USING (auth.uid() = user_id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'corporate_accounts' 
    AND policyname = 'Users can update own corporate account'
  ) THEN
    CREATE POLICY "Users can update own corporate account" ON corporate_accounts FOR UPDATE USING (auth.uid() = user_id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'corporate_accounts' 
    AND policyname = 'Users can insert own corporate account'
  ) THEN
    CREATE POLICY "Users can insert own corporate account" ON corporate_accounts FOR INSERT WITH CHECK (auth.uid() = user_id);
  END IF;
END $$; 