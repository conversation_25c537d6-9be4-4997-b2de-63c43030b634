# Corporate Signup Integration with Supabase

This document outlines the complete integration of corporate signup functionality with Supabase for the Kredxa platform.

## Overview

The corporate signup integration provides a multi-step registration process for corporate entities (companies, organizations) to join the platform as lenders. The integration includes:

- Multi-step signup form (4 steps)
- Email verification
- Organization details collection
- Document upload functionality
- Database integration with Supabase
- Row Level Security (RLS) policies

## Database Schema

### Tables

1. **profiles** - User authentication and basic profile information
2. **corporate_accounts** - Corporate-specific account details
3. **documents** - Document uploads and verification status
4. **individual_accounts** - Individual user accounts (for reference)

### Corporate Accounts Table Fields

```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key to auth.users)
- email (TEXT)
- organization_name (TEXT)
- office_address (TEXT)
- contact_person (TEXT)
- contact_phone (TEXT)
- business_type (TEXT)
- registration_number (TEXT)
- tax_identification_number (TEXT)
- website (TEXT)
- industry (TEXT)
- company_size (ENUM: '1-10', '11-50', '51-200', '201-500', '500+')
- years_in_business (INTEGER)
- annual_revenue (DECIMAL)
- bank_name (TEXT)
- account_number (TEXT)
- account_name (TEXT)
- approval_status (ENUM: 'pending', 'approved', 'rejected')
- verification_status (ENUM: 'pending', 'verified', 'rejected')
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

## Setup Instructions

### 1. Environment Variables

Create a `.env.local` file in your project root with:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 2. Database Migrations

Run the following migrations in your Supabase project SQL editor:

1. `001_initial_schema.sql` - Initial database schema
2. `002_update_schema.sql` - Updated schema with improvements
3. `003_add_employment_guarantor_fields.sql` - Additional individual fields
4. `004_enhance_corporate_accounts.sql` - Enhanced corporate account fields

### 3. Storage Bucket

Create a storage bucket named "documents" in your Supabase project:

1. Go to Storage in your Supabase dashboard
2. Click "Create a new bucket"
3. Name it "documents"
4. Set it to private (recommended for security)

### 4. RLS Policies

The migrations automatically create Row Level Security policies:

- Users can only view their own corporate account
- Users can only update their own corporate account
- Users can only insert their own corporate account
- Users can only view/update their own documents

## API Functions

### Core Functions

```typescript
// Corporate signup
signUpCorporate(email: string, password: string, additionalData?: CorporateSignupData)

// Update corporate account
updateCorporateAccount(userId: string, updates: CorporateAccountUpdates)

// Get corporate account
getCorporateAccount(userId: string)

// Upload document
uploadDocument(userId: string, file: File, documentType: string)

// Email verification
verifyEmailOTP(email: string, otp: string)
```

### Data Interfaces

```typescript
interface CorporateSignupData {
  email: string;
  password: string;
  organizationName?: string;
  officeAddress?: string;
  contactPerson?: string;
  contactPhone?: string;
  businessType?: string;
  registrationNumber?: string;
  taxIdentificationNumber?: string;
  website?: string;
  industry?: string;
  companySize?: '1-10' | '11-50' | '51-200' | '201-500' | '500+';
  yearsInBusiness?: number;
  annualRevenue?: number;
  bankName?: string;
  accountNumber?: string;
  accountName?: string;
  documents?: {
    cac?: File | null;
    mermet?: File | null;
    utilityBill?: File | null;
    lendersLicense?: File | null;
  };
}
```

## Signup Flow

### Step 1: Account Creation
- User enters email and password
- System validates email domain (rejects public email providers)
- Creates Supabase auth user with `user_type: 'corporate'` and `mode: 'lender'`
- Creates corporate account record with `approval_status: 'pending'`

### Step 2: Email Verification
- System sends verification email via Supabase
- User enters OTP code
- System verifies email using Supabase OTP verification

### Step 3: Organization Details
- User enters organization information
- System updates corporate account with organization details
- All fields are optional but recommended

### Step 4: Document Upload
- User uploads required documents:
  - CAC Certificate
  - Memorandum & Articles of Association
  - Utility Bill
  - Lender's License (if applicable)
- Documents are stored in Supabase Storage
- Document records are created in the documents table

## Testing

### Test Page

Visit `/test-db` to run integration tests:

1. **General Database Test** - Tests basic database connectivity
2. **Corporate Signup Test** - Tests corporate signup specific functionality

### Manual Testing

1. Navigate to `/auth/corporate/signup`
2. Complete the 4-step signup process
3. Check Supabase dashboard for:
   - User in Authentication > Users
   - Corporate account in Table Editor > corporate_accounts
   - Documents in Storage > documents
   - Document records in Table Editor > documents

## Error Handling

The integration includes comprehensive error handling:

- **Validation errors** - Client-side form validation
- **Authentication errors** - Supabase auth error messages
- **Database errors** - Detailed logging and user-friendly messages
- **Storage errors** - File upload error handling
- **Network errors** - Connection timeout handling

## Security Features

1. **Row Level Security (RLS)** - Users can only access their own data
2. **Email domain validation** - Prevents public email providers
3. **File type validation** - Only allows specific document types
4. **Secure file storage** - Documents stored in private bucket
5. **Password requirements** - Minimum 6 characters
6. **Email verification** - Required before proceeding

## Monitoring and Logging

All functions include comprehensive logging:

```typescript
console.log('Starting corporate signup for:', email);
console.log('Auth signup successful, user ID:', data.user?.id);
console.log('Corporate account created successfully');
console.log('Document uploaded successfully:', fileName);
```

## Troubleshooting

### Common Issues

1. **"Corporate accounts table error"**
   - Run the database migrations
   - Check table exists in Supabase dashboard

2. **"Storage error"**
   - Create "documents" storage bucket
   - Check bucket permissions

3. **"Email verification failed"**
   - Check Supabase email settings
   - Verify email template configuration

4. **"RLS policy error"**
   - Ensure RLS is enabled on tables
   - Check policy definitions in migrations

### Debug Steps

1. Check browser console for detailed error messages
2. Verify environment variables are set correctly
3. Test database connection at `/test-db`
4. Check Supabase dashboard for user creation
5. Verify storage bucket exists and is accessible

## Future Enhancements

Potential improvements for the corporate signup integration:

1. **Additional verification steps** - Phone verification, address verification
2. **Document processing** - OCR for document text extraction
3. **Approval workflow** - Admin approval process
4. **Integration with external services** - Business verification APIs
5. **Advanced analytics** - Signup funnel analysis
6. **Multi-language support** - Internationalization
7. **Progressive web app features** - Offline support, push notifications

## Support

For issues with the corporate signup integration:

1. Check the troubleshooting section above
2. Review the test page at `/test-db`
3. Check Supabase dashboard for errors
4. Review browser console for detailed logs
5. Verify all setup steps have been completed 