"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { testDatabaseConnection, testCorporateSignupIntegration } from "@/lib/auth-supabase";

interface TestResult {
  success: boolean;
  message?: string;
  error?: string;
  details?: {
    tables: string[];
    storage: string;
    rls: string;
  };
}

export default function TestDatabasePage() {
  const [generalTestResult, setGeneralTestResult] = useState<TestResult | null>(null);
  const [corporateTestResult, setCorporateTestResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCorporateLoading, setIsCorporateLoading] = useState(false);

  const runGeneralTest = async () => {
    setIsLoading(true);
    try {
      const result = await testDatabaseConnection();
      setGeneralTestResult(result);
    } catch {
      setGeneralTestResult({ success: false, error: 'Test failed' });
    } finally {
      setIsLoading(false);
    }
  };

  const runCorporateTest = async () => {
    setIsCorporateLoading(true);
    try {
      const result = await testCorporateSignupIntegration();
      setCorporateTestResult(result);
    } catch {
      setCorporateTestResult({ success: false, error: 'Test failed' });
    } finally {
      setIsCorporateLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Database Integration Tests</h1>
          <p className="mt-2 text-gray-600">Test the database connection and corporate signup integration</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* General Database Test */}
          <Card>
            <CardHeader>
              <CardTitle>General Database Connection</CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={runGeneralTest} 
                disabled={isLoading}
                className="w-full mb-4"
              >
                {isLoading ? "Testing..." : "Run General Test"}
              </Button>
              
              {generalTestResult && (
                <div className={`p-4 rounded-md ${
                  generalTestResult.success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <h4 className={`font-medium ${
                    generalTestResult.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {generalTestResult.success ? '✓ Test Passed' : '✗ Test Failed'}
                  </h4>
                  <p className={`text-sm mt-1 ${
                    generalTestResult.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {generalTestResult.message || generalTestResult.error}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Corporate Signup Integration Test */}
          <Card>
            <CardHeader>
              <CardTitle>Corporate Signup Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={runCorporateTest} 
                disabled={isCorporateLoading}
                className="w-full mb-4"
              >
                {isCorporateLoading ? "Testing..." : "Run Corporate Test"}
              </Button>
              
              {corporateTestResult && (
                <div className={`p-4 rounded-md ${
                  corporateTestResult.success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <h4 className={`font-medium ${
                    corporateTestResult.success ? 'text-green-800' : 'text-red-800'
                  }`}>
                    {corporateTestResult.success ? '✓ Test Passed' : '✗ Test Failed'}
                  </h4>
                  <p className={`text-sm mt-1 ${
                    corporateTestResult.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {corporateTestResult.message || corporateTestResult.error}
                  </p>
                  {corporateTestResult.details && (
                    <div className="mt-2 text-xs text-gray-600">
                      <p>Tables: {corporateTestResult.details.tables.join(', ')}</p>
                      <p>Storage: {corporateTestResult.details.storage}</p>
                      <p>RLS: {corporateTestResult.details.rls}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Setup Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-medium text-gray-900">1. Environment Variables</h4>
                <p className="text-gray-600">Ensure your <code>.env.local</code> file contains:</p>
                <pre className="bg-gray-100 p-2 rounded mt-1 text-xs">
{`NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key`}
                </pre>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">2. Database Migrations</h4>
                <p className="text-gray-600">Run the following migrations in your Supabase project:</p>
                <ul className="list-disc list-inside text-gray-600 mt-1">
                  <li>001_initial_schema.sql</li>
                  <li>002_update_schema.sql</li>
                  <li>003_add_employment_guarantor_fields.sql</li>
                  <li>004_enhance_corporate_accounts.sql</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">3. Storage Bucket</h4>
                <p className="text-gray-600">Create a storage bucket named &quot;documents&quot; in your Supabase project for file uploads.</p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900">4. Test Results</h4>
                <p className="text-gray-600">Both tests should pass for the corporate signup integration to work properly.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 