"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { 
  signUpCorporate, 
  updateCorporateAccount, 
  uploadDocument, 
  verifyEmailOTP
} from "@/lib/auth-supabase";

export default function CorporateSignupPage() {
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    organizationName: "",
    officeAddress: "",
    documents: {
      cac: null as File | null,
      mermet: null as File | null,
      utilityBill: null as File | null,
      lendersLicense: null as File | null
    }
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setFormData(prev => ({
        ...prev,
        documents: {
          ...prev.documents,
          [name]: files[0]
        }
      }));
    }
  };

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    } else {
      router.back();
    }
  };

  const handleAccountCreation = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters long");
      setIsLoading(false);
      return;
    }

    // Check if email is corporate (not public email domains)
    const domain = formData.email.split('@')[1]?.toLowerCase();
    const publicDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'aol.com'];
    if (domain && publicDomains.includes(domain)) {
      setError("Please use your company email address, not a public email domain");
      setIsLoading(false);
      return;
    }

    try {
      const result = await signUpCorporate(formData.email, formData.password);
      if (result.success && result.user) {
        setUserId(result.user.id);
        setStep(2);
      } else {
        setError(result.error || "Account creation failed");
      }
    } catch {
      setError("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    const formDataObj = new FormData(e.target as HTMLFormElement);
    const verificationCode = formDataObj.get('verificationCode') as string;

    if (!verificationCode) {
      setError("Please enter the verification code");
      setIsLoading(false);
      return;
    }

    try {
      // Use actual Supabase email verification
      const result = await verifyEmailOTP(formData.email, verificationCode);
      
      if (result.success) {
        setStep(3);
      } else {
        setError(result.error || "Email verification failed. Please check the code and try again.");
      }
    } catch {
      setError("Email verification failed. Please check the code and try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOrganizationDetails = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Update corporate account with organization details
    try {
      if (userId) {
        const result = await updateCorporateAccount(userId, {
          organization_name: formData.organizationName,
          office_address: formData.officeAddress
        });
        
        if (result.success) {
          setStep(4);
        } else {
          setError(result.error || "Failed to save organization details");
        }
      } else {
        setError("User session not found");
      }
    } catch {
      setError("Failed to save organization details. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    // Upload documents
    try {
      if (!userId) {
        setError("User session not found");
        return;
      }

      const documentTypes = ['cac', 'mermet', 'utilityBill', 'lendersLicense'] as const;
      let uploadSuccess = true;

      for (const docType of documentTypes) {
        const file = formData.documents[docType];
        if (file) {
          const result = await uploadDocument(userId, file, docType);
          if (!result.success) {
            uploadSuccess = false;
            setError(`Failed to upload ${docType}: ${result.error}`);
            break;
          }
        }
      }

      if (uploadSuccess) {
        setSuccess(true);
        // Redirect to pending approval page
        setTimeout(() => {
          router.push('/auth/corporate/pending-approval');
        }, 2000);
      }
    } catch {
      setError("Document upload failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <form onSubmit={handleAccountCreation} className="space-y-4">
            <h2 className="text-2xl font-bold mb-2">Create Your Corporate Account</h2>
            <p className="mb-6 text-gray-600 text-sm">Step 1: Enter your official email and password</p>
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Official Email
                </label>
                <Input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Enter your company email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="mt-1"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Use your company email address (not Gmail, Yahoo, etc.)
                </p>
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <div className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    placeholder="Create a password (min 6 characters)"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className="mt-1"
                  />
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7.846-8 12.324-8 4.477 0 8.268 2.943 9.542 7 1.274 4.057 5.064 7.846 8 12.324 8" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7.846-8 12.324-8 4.477 0 8.268 2.943 9.542 7 1.274 4.057 5.064 7.846 8 12.324 8" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Confirm Password
                </label>
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    id="confirmPassword"
                    name="confirmPassword"
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                    className="mt-1"
                  />
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-black" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7.846-8 12.324-8 4.477 0 8.268 2.943 9.542 7 1.274 4.057 5.064 7.846 8 12.324 8" />
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7.846-8 12.324-8 4.477 0 8.268 2.943 9.542 7 1.274 4.057 5.064 7.846 8 12.324 8" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={handleBack} className="flex-1">
                Back
              </Button>
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? "Creating Account..." : "Continue"}
              </Button>
            </div>
          </form>
        );

      case 2:
        return (
          <form onSubmit={handleEmailVerification} className="space-y-4">
            <h2 className="text-2xl font-bold mb-2">Verify Your Email</h2>
            <p className="mb-6 text-gray-600 text-sm">
              We&apos;ve sent a verification code to {formData.email}
            </p>
            <div>
              <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 mb-1">
                Verification Code
              </label>
              <Input
                type="text"
                id="verificationCode"
                name="verificationCode"
                placeholder="Enter the 6-digit code"
                required
                maxLength={6}
                className="mt-1"
              />
              <p className="mt-1 text-xs text-gray-500">
                Check your email for the verification code
              </p>
            </div>
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={handleBack} className="flex-1">
                Back
              </Button>
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? "Verifying..." : "Verify Email"}
              </Button>
            </div>
          </form>
        );

      case 3:
        return (
          <form onSubmit={handleOrganizationDetails} className="space-y-4">
            <h2 className="text-2xl font-bold mb-2">Organization Details</h2>
            <p className="mb-6 text-gray-600 text-sm">Step 3: Enter your organization information</p>
            <div className="space-y-4">
              <div>
                <label htmlFor="organizationName" className="block text-sm font-medium text-gray-700 mb-1">
                  Organization Name
                </label>
                <Input
                  type="text"
                  id="organizationName"
                  name="organizationName"
                  placeholder="Enter your organization name"
                  value={formData.organizationName}
                  onChange={handleInputChange}
                  required
                  className="mt-1"
                />
              </div>
              <div>
                <label htmlFor="officeAddress" className="block text-sm font-medium text-gray-700 mb-1">
                  Office Address
                </label>
                <Input
                  type="text"
                  id="officeAddress"
                  name="officeAddress"
                  placeholder="Enter your office address"
                  value={formData.officeAddress}
                  onChange={handleInputChange}
                  required
                  className="mt-1"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={handleBack} className="flex-1">
                Back
              </Button>
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? "Saving..." : "Continue"}
              </Button>
            </div>
          </form>
        );

      case 4:
        return (
          <form onSubmit={handleDocumentUpload} className="space-y-4">
            <h2 className="text-2xl font-bold mb-2">Upload Documents</h2>
            <p className="mb-6 text-gray-600 text-sm">Step 4: Upload required documents</p>
            <div className="space-y-4">
              <div>
                <label htmlFor="cac" className="block text-sm font-medium text-gray-700 mb-1">
                  CAC Certificate
                </label>
                <Input
                  type="file"
                  id="cac"
                  name="cac"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                  required
                  className="mt-1"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Upload your Certificate of Incorporation
                </p>
              </div>
              <div>
                <label htmlFor="mermet" className="block text-sm font-medium text-gray-700 mb-1">
                  Mermet
                </label>
                <Input
                  type="file"
                  id="mermet"
                  name="mermet"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                  required
                  className="mt-1"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Upload your Memorandum & Articles of Association
                </p>
              </div>
              <div>
                <label htmlFor="utilityBill" className="block text-sm font-medium text-gray-700 mb-1">
                  Utility Bill
                </label>
                <Input
                  type="file"
                  id="utilityBill"
                  name="utilityBill"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                  required
                  className="mt-1"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Recent utility bill for address verification
                </p>
              </div>
              <div>
                <label htmlFor="lendersLicense" className="block text-sm font-medium text-gray-700 mb-1">
                  Lender&apos;s License
                </label>
                <Input
                  type="file"
                  id="lendersLicense"
                  name="lendersLicense"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                  required
                  className="mt-1"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Financial institution license (if applicable)
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={handleBack} className="flex-1">
                Back
              </Button>
              <Button type="submit" disabled={isLoading} className="flex-1">
                {isLoading ? "Uploading..." : "Submit Application"}
              </Button>
            </div>
          </form>
        );

      default:
        return null;
    }
  };

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                  <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">Application Submitted Successfully!</h3>
                <p className="mt-2 text-sm text-gray-600">
                  Your corporate account application has been submitted for review. You will be redirected to the pending approval page shortly.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <main className="flex flex-1 items-center justify-center pt-20">
        <div className="flex w-full max-w-4xl bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Left: Signup Form */}
          <div className="w-full md:w-1/2 p-8 flex flex-col justify-center">
            <div className="text-center mb-6">
              <p className="text-gray-600">
                Already have an account?{" "}
                <Link href="/auth/corporate/login" className="text-[#1A0505] font-semibold hover:underline">
                  Sign in here
                </Link>
              </p>
            </div>
            
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}
            
            {renderStep()}
            
            <div className="mt-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex space-x-2">
                  {[1, 2, 3, 4].map((stepNumber) => (
                    <div
                      key={stepNumber}
                      className={`w-2 h-2 rounded-full ${
                        stepNumber <= step ? "bg-[#1A0505]" : "bg-gray-200"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500">Step {step} of 4</span>
              </div>
            </div>
            
            <div className="flex justify-between items-center mt-6 text-sm">
              <div className="flex space-x-4 text-gray-400">
                <Link href="#">FAQs</Link>
                <Link href="#">Terms & Conditions</Link>
                <Link href="#">Privacy Policy</Link>
              </div>
            </div>
          </div>
          
          {/* Right: Welcome Panel */}
          <div className="hidden md:flex w-1/2 bg-gradient-to-br from-gray-900 to-gray-800 text-white flex-col items-center justify-center p-8 rounded-r-xl">
            <div className="flex flex-col items-center">
              <div className="mb-4">
                <svg width="40" height="40" fill="none" viewBox="0 0 24 24">
                  <path fill="#fff" d="M12 2C6.48 2 2 6.48 2 12c0 5.52 4.48 10 10 10s10-4.48 10-10C22 6.48 17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8 0-4.41 3.59-8 8-8s8 3.59 8 8c0 4.41-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">Welcome to Kredxa</h3>
              <p className="mb-4 text-center text-sm text-gray-200">
                Your one-stop marketplace for loans. Fast, transparent, and secure for both borrowers and lenders.
              </p>
              <ul className="text-sm space-y-2">
                <li className="flex items-center"><span className="mr-2">✔</span>Bank-level security</li>
                <li className="flex items-center"><span className="mr-2">✔</span>Personal & SME loans</li>
                <li className="flex items-center"><span className="mr-2">✔</span>Trusted by leading lenders</li>
              </ul>
            </div>
          </div>
        </div>
      </main>
      
      <footer className="bg-white border-t mt-8 px-8 py-6 flex flex-col md:flex-row justify-between items-center text-xs text-gray-500">
        <div className="mb-2 md:mb-0">
          <div className="font-semibold">Kredxa</div>
          <div>The modern loan marketplace for individuals & SMEs. Compare, apply and manage loans with confidence.</div>
        </div>
        <div className="flex flex-col md:flex-row md:space-x-8 items-center">
          <div className="mb-2 md:mb-0">
            <div className="font-semibold">Quick Links</div>
            <div><Link href="#">About</Link></div>
            <div><Link href="#">Terms & Conditions</Link></div>
            <div><Link href="#">Privacy Policy</Link></div>
            <div><Link href="#">Contact Us</Link></div>
          </div>
          <div className="flex space-x-4 mt-2 md:mt-0">
            <a href="#" aria-label="Twitter">
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.46 6c-.77.35-1.6.58-2.47.69a4.3 4.3 0 0 0 1.88-2.37 8.59 8.59 0 0 1-2.72 1.04A4.28 4.28 0 0 0 16.11 4c-2.37 0-4.29 1.92-4.29 4.29 0 .34.04.67.11.99C7.69 9.13 4.07 7.38 1.64 4.7c-.37.64-.58 1.38-.58 2.17 0 1.5.76 2.82 1.92 3.6-.7-.02-1.36-.21-1.94-.53v.05c0 2.1 1.5 3.85 3.5 4.25-.36.1-.74.16-1.13.16-.28 0-.54-.03-.8-.08.54 1.7 2.1 2.94 3.95 2.97A8.6 8.6 0 0 1 2 19.54c-.29 0-.57-.02-.85-.05A12.13 12.13 0 0 0 8.29 21.5c7.55 0 11.68-6.26 11.68-11.68 0-.18-.01-.36-.02-.54A8.18 8.18 0 0 0 24 4.59a8.36 8.36 0 0 1-2.54.7z"/>
              </svg>
            </a>
            <a href="#" aria-label="Facebook">
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.68 0H1.32C.59 0 0 .59 0 1.32v21.36C0 23.41.59 24 1.32 24h11.49v-9.29H9.69v-3.62h3.12V8.41c0-3.1 1.89-4.79 4.65-4.79 1.32 0 2.45.1 2.78.14v3.22h-1.91c-1.5 0-1.79.71-1.79 1.75v2.3h3.58l-.47 3.62h-3.11V24h6.09c.73 0 1.32-.59 1.32-1.32V1.32C24 .59 23.41 0 22.68 0z"/>
              </svg>
            </a>
            <a href="#" aria-label="Instagram">
              <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.16c3.2 0 3.584.012 4.85.07 1.17.056 1.97.24 2.43.41.59.22 1.01.48 1.45.92.44.44.7.86.92 1.45.17.46.35 1.26.41 2.43.058 1.266.07 1.65.07 4.85s-.012 3.584-.07 4.85c-.056 1.17-.24 1.97-.41 2.43-.22.59-.48 1.01-.92 1.45-.44.44-.86.7-1.45.92-.46.17-1.26.35-2.43.41-1.266.058-1.65.07-4.85.07s-3.584-.012-4.85-.07c-1.17-.056-1.97-.24-2.43-.41-.59-.22-1.01-.48-1.45-.92-.44-.44-.7-.86-.92-1.45-.17-.46-.35-1.26-.41-2.43C2.172 15.784 2.16 15.4 2.16 12s.012-3.584.07-4.85c.056-1.17.24-1.97.41-2.43.22-.59.48-1.01.92-1.45.44-.44.86-.7 1.45-.92.46-.17 1.26-.35 2.43-.41C8.416 2.172 8.8 2.16 12 2.16zm0-2.16C8.736 0 8.332.012 7.052.07c-1.28.058-2.16.24-2.91.51-.8.29-1.48.67-2.15 1.34-.67.67-1.05 1.35-1.34 2.15-.27.75-.45 1.63-.51 2.91C.012 8.332 0 8.736 0 12c0 3.264.012 3.668.07 4.948.058 1.28.24 2.16.51 2.91.29.8.67 1.48 1.34 2.15.67.67 1.35 1.05 2.15 1.34.75.27 1.63.45 2.91.51C8.332 23.988 8.736 24 12 24c3.264 0 3.668-.012 4.948-.07 1.28-.058 2.16-.24 2.91-.51.8-.29 1.48-.67 2.15-1.34.67-.67 1.05-1.35 1.34-2.15.27-.75.45-1.63.51-2.91.058-1.28.07-1.684.07-4.948 0-3.264-.012-3.668-.07-4.948-.058-1.28-.24-2.16-.51-2.91-.29-.8-.67-1.48-1.34-2.15-.67-.67-1.35-1.05-2.15-1.34-.75-.27-1.63-.45-2.91-.51C15.668.012 15.264 0 12 0zm0 5.838a6.162 6.162 0 1 0 0 12.324 6.162 6.162 0 0 0 0-12.324zm0 10.162a3.999 3.999 0 1 1 0-7.998 3.999 3.999 0 0 1 0 7.998zm7.844-10.406a1.44 1.44 0 1 0 0 2.88 1.44 1.44 0 0 0 0-2.88z"/>
              </svg>
            </a>
          </div>
        </div>
        <div className="mt-4 md:mt-0">© 2024 Kredxa. All rights reserved.</div>
      </footer>
    </div>
  );
} 