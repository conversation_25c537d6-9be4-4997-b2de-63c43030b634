"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

// Mock data for notifications
const notifications = [
  {
    id: 1,
    type: "loan",
    title: "Loan Application Approved",
    message: "Your loan application for ₦500,000 has been approved by First Bank.",
    date: "2024-03-20T10:30:00",
    read: false,
    icon: "✅",
  },
  {
    id: 2,
    type: "payment",
    title: "Payment Due Reminder",
    message: "Your next payment of ₦83,333 is due in 3 days.",
    date: "2024-03-19T15:45:00",
    read: false,
    icon: "💰",
  },
  {
    id: 3,
    type: "system",
    title: "Profile Update Required",
    message: "Please complete your KYC verification to access additional features.",
    date: "2024-03-18T09:15:00",
    read: true,
    icon: "⚙️",
  },
  {
    id: 4,
    type: "offer",
    title: "New Loan Offer",
    message: "You have a new loan offer from GTBank with competitive rates.",
    date: "2024-03-17T14:20:00",
    read: true,
    icon: "🎁",
  },
  {
    id: 5,
    type: "payment",
    title: "Payment Successful",
    message: "Your payment of ₦83,333 has been processed successfully.",
    date: "2024-03-16T11:00:00",
    read: true,
    icon: "✅",
  },
];

const getNotificationColor = (type: string) => {
  switch (type) {
    case "loan":
      return "bg-blue-100 text-blue-800";
    case "payment":
      return "bg-green-100 text-green-800";
    case "system":
      return "bg-gray-100 text-gray-800";
    case "offer":
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function NotificationsPage() {
  const [filter, setFilter] = useState<string>("all");
  const [notificationsList, setNotificationsList] = useState(notifications);

  const filteredNotifications = filter === "all" 
    ? notificationsList 
    : notificationsList.filter(n => n.type === filter);

  const markAsRead = (id: number) => {
    setNotificationsList(notificationsList.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };

  const markAllAsRead = () => {
    setNotificationsList(notificationsList.map(notification => ({
      ...notification,
      read: true
    })));
  };

  const unreadCount = notificationsList.filter(n => !n.read).length;

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Notifications</h1>
          <p className="text-gray-500 text-sm mt-1">
            {unreadCount} unread {unreadCount === 1 ? "notification" : "notifications"}
          </p>
        </div>
        <Button 
          variant="outline" 
          onClick={markAllAsRead}
          disabled={unreadCount === 0}
        >
          Mark all as read
        </Button>
      </div>

      {/* Filter Buttons */}
      <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
        <Button
          variant={filter === "all" ? "default" : "outline"}
          onClick={() => setFilter("all")}
          className="whitespace-nowrap"
        >
          All
        </Button>
        <Button
          variant={filter === "loan" ? "default" : "outline"}
          onClick={() => setFilter("loan")}
          className="whitespace-nowrap"
        >
          Loans
        </Button>
        <Button
          variant={filter === "payment" ? "default" : "outline"}
          onClick={() => setFilter("payment")}
          className="whitespace-nowrap"
        >
          Payments
        </Button>
        <Button
          variant={filter === "system" ? "default" : "outline"}
          onClick={() => setFilter("system")}
          className="whitespace-nowrap"
        >
          System
        </Button>
        <Button
          variant={filter === "offer" ? "default" : "outline"}
          onClick={() => setFilter("offer")}
          className="whitespace-nowrap"
        >
          Offers
        </Button>
      </div>

      {/* Notifications List */}
      <div className="space-y-4">
        {filteredNotifications.map((notification) => (
          <Card 
            key={notification.id}
            className={`transition-colors ${!notification.read ? 'bg-gray-50' : ''}`}
          >
            <CardContent className="p-4">
              <div className="flex items-start gap-4">
                <div className="text-2xl">{notification.icon}</div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">{notification.title}</h3>
                    <span className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${getNotificationColor(notification.type)}`}>
                      {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                    </span>
                  </div>
                  <p className="text-gray-600 mt-1">{notification.message}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm text-gray-500">
                      {new Date(notification.date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                      >
                        Mark as read
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
} 