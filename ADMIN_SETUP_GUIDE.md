# Admin Setup Guide

This guide explains how to set up admin accounts for the Kredxa platform.

## Overview

The admin system provides role-based access control with three levels:
- **Super Admin**: Full system access, can create/remove other admins
- **Admin**: Full admin panel access, can manage accounts and documents
- **Moderator**: Limited admin access (for future use)

## Prerequisites

1. **Database Setup**: All migrations must be run, including the admin roles migration
2. **User Account**: You must have a regular user account (individual or corporate)
3. **Database Access**: Supabase connection must be working

## Step-by-Step Setup

### 1. Run Database Migrations

First, ensure all migrations are applied to your Supabase project:

```sql
-- Run these migrations in order:
-- 001_initial_schema.sql
-- 002_update_schema.sql
-- 003_add_employment_guarantor_fields.sql
-- 004_enhance_corporate_accounts.sql
-- 005_add_admin_roles.sql  ← This is the key one for admin functionality
```

### 2. Create a Regular User Account

1. Sign up for a regular account at `/auth/individual/signup` or `/auth/corporate/signup`
2. Complete the signup process and verify your email
3. Ensure you can log in successfully

### 3. Set Up the First Admin

#### Option A: Using the Admin Setup Page (Recommended)

1. Navigate to `/admin/setup`
2. You'll see a setup form if no admin users exist
3. Enter your email and click "Setup Super Admin"
4. You'll be redirected to the admin panel

#### Option B: Using the Main Admin Page

1. Navigate to `/admin`
2. If no admins exist, you'll see a setup mode
3. Follow the setup instructions

#### Option C: Manual Database Setup

If the UI setup doesn't work, you can manually set admin status:

```sql
-- Replace '<EMAIL>' with your actual email
UPDATE profiles 
SET is_admin = TRUE, admin_level = 'super_admin'
WHERE email = '<EMAIL>';

-- Insert into admin_users table
INSERT INTO admin_users (user_id, email, admin_level, permissions, created_by)
SELECT 
  id, 
  email, 
  'super_admin', 
  '{}', 
  id
FROM auth.users 
WHERE email = '<EMAIL>';
```

### 4. Verify Admin Access

1. Log out and log back in
2. Navigate to `/admin`
3. You should now have access to the admin dashboard

## Admin Functions

### Creating Additional Admin Users

Once you have super admin access, you can create additional admin users:

1. Go to the admin panel
2. Use the admin management functions (to be implemented)
3. Or use the database function:

```sql
-- Only super admins can run this
SELECT create_admin_user('<EMAIL>', 'admin');
```

### Removing Admin Users

```sql
-- Only super admins can run this
SELECT remove_admin_user('<EMAIL>');
```

## Troubleshooting

### "Access Denied" Error

**Problem**: You see "Access Denied" when trying to access `/admin`

**Solutions**:
1. Ensure you're logged in
2. Check if the admin migration has been run
3. Verify no admin users exist in the system
4. Try the setup page at `/admin/setup`

### "Admin users already exist" Error

**Problem**: Setup page says admin users already exist

**Solutions**:
1. Contact an existing admin for access
2. Or manually remove existing admins (if you have database access):

```sql
-- Check existing admins
SELECT * FROM admin_users WHERE is_active = TRUE;

-- Remove admin status (be careful!)
UPDATE profiles SET is_admin = FALSE, admin_level = NULL;
UPDATE admin_users SET is_active = FALSE;
```

### Database Migration Errors

**Problem**: Migration fails to run

**Solutions**:
1. Check if tables already exist
2. Run migrations in order
3. Check Supabase logs for specific errors

### "Function not found" Error

**Problem**: Database functions like `create_admin_user` don't exist

**Solutions**:
1. Ensure migration `005_add_admin_roles.sql` was run completely
2. Check that all functions were created successfully
3. Re-run the migration if needed

## Security Considerations

### Admin Role Security

- Only super admins can create/remove other admins
- Admin users can only access admin data through RLS policies
- All admin actions are logged

### Best Practices

1. **Use Strong Passwords**: Admin accounts should have strong passwords
2. **Limit Admin Access**: Only give admin access to trusted users
3. **Regular Review**: Periodically review admin user list
4. **Backup Admin**: Always have at least one backup super admin

### Environment Variables

Ensure your environment variables are set correctly:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Admin Panel Features

Once you have admin access, you can:

1. **View Statistics**: See account counts and pending items
2. **Manage Individual Accounts**: Verify/reject individual users
3. **Manage Corporate Accounts**: Approve/reject corporate users
4. **Manage Documents**: Verify/reject uploaded documents
5. **Search Accounts**: Find specific users by email, name, or status
6. **View Account Details**: See complete user information
7. **Refresh Data**: Reload latest data from database

## API Functions

The admin system includes these functions:

```typescript
// Check if user is admin
checkAdminStatus(userId: string)

// Create admin user (super admin only)
createAdminUser(email: string, level: 'super_admin' | 'admin' | 'moderator')

// Remove admin user (super admin only)
removeAdminUser(email: string)

// Get all admin users
getAllAdminUsers()

// Set admin status manually
setAdminStatus(userId: string, isAdmin: boolean, level?: string)
```

## Support

If you encounter issues:

1. Check the browser console for errors
2. Verify database migrations are complete
3. Check Supabase dashboard for errors
4. Review the troubleshooting section above
5. Ensure all prerequisites are met

## Next Steps

After setting up admin access:

1. Review existing accounts and documents
2. Set up additional admin users if needed
3. Configure admin permissions as required
4. Test all admin functions
5. Set up monitoring and alerts 