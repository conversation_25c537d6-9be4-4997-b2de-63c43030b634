import { supabase } from './supabase';
import { Database } from './supabase';

// Types for payment system
export type LoanPayment = Database['public']['Tables']['loan_payments']['Row'];
export type PaymentTransaction = Database['public']['Tables']['payment_transactions']['Row'];
export type PaymentSchedule = Database['public']['Tables']['payment_schedules']['Row'];

export interface PaymentInitData {
  amount: number;
  email: string;
  loanApplicationId: string;
  scheduledPaymentMonth?: number;
  paymentType?: 'regular' | 'principal_only' | 'early_payment' | 'late_payment';
}

export interface PaystackInitResponse {
  status: boolean;
  message: string;
  data?: {
    authorization_url: string;
    access_code: string;
    reference: string;
  };
}

export interface PaystackVerifyResponse {
  status: boolean;
  message: string;
  data?: {
    reference: string;
    amount: number;
    currency: string;
    transaction_date: string;
    status: string;
    gateway_response: string;
    authorization: {
      authorization_code: string;
      card_type: string;
      last4: string;
      bank: string;
    };
  };
}

// Paystack configuration moved to API routes for security

// Initialize Paystack payment via API route
export async function initializePaystackPayment(data: PaymentInitData): Promise<PaystackInitResponse> {
  try {
    const response = await fetch('/api/payments/initialize', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: data.amount,
        email: data.email,
        loanApplicationId: data.loanApplicationId,
        scheduledPaymentMonth: data.scheduledPaymentMonth,
        paymentType: data.paymentType || 'regular'
      })
    });

    const result = await response.json();
    
    if (!response.ok || !result.success) {
      console.error('Payment initialization failed:', {
        status: response.status,
        statusText: response.statusText,
        result,
        url: response.url
      });
      throw new Error(result.message || `HTTP ${response.status}: Failed to initialize payment`);
    }

    return {
      status: true,
      message: 'Payment initialized successfully',
      data: result.data
    };
  } catch (error) {
    console.error('Error initializing Paystack payment:', error);
    
    // Enhanced error message for debugging
    let errorMessage = 'Failed to initialize payment';
    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Add helpful context for common issues
      if (error.message.includes('fetch')) {
        errorMessage += ' (Network error - check if API routes are deployed)';
      } else if (error.message.includes('not configured')) {
        errorMessage += ' (Check environment variables on deployment platform)';
      }
    }
    
    return {
      status: false,
      message: errorMessage
    };
  }
}

// Verify Paystack payment via API route
export async function verifyPaystackPayment(reference: string): Promise<PaystackVerifyResponse> {
  try {
    const response = await fetch('/api/payments/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reference })
    });

    const result = await response.json();
    
    if (!response.ok || !result.success) {
      throw new Error(result.message || 'Failed to verify payment');
    }

    return {
      status: true,
      message: 'Payment verified successfully',
      data: result.data
    };
  } catch (error) {
    console.error('Error verifying Paystack payment:', error);
    return {
      status: false,
      message: error instanceof Error ? error.message : 'Failed to verify payment'
    };
  }
}

// Create loan payment record
export async function createLoanPayment(
  loanApplicationId: string,
  paymentData: {
    amount: number;
    paymentType?: string;
    scheduledPaymentMonth?: number;
    principalAmount?: number;
    interestAmount?: number;
    processingFee?: number;
    lateFee?: number;
    paymentReference: string;
  }
): Promise<{ success: boolean; data?: LoanPayment; error?: string }> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('loan_payments')
      .insert({
        loan_application_id: loanApplicationId,
        borrower_id: user.id,
        payment_amount: paymentData.amount,
        payment_type: paymentData.paymentType || 'regular',
        scheduled_payment_month: paymentData.scheduledPaymentMonth,
        principal_amount: paymentData.principalAmount || 0,
        interest_amount: paymentData.interestAmount || 0,
        processing_fee: paymentData.processingFee || 0,
        late_fee: paymentData.lateFee || 0,
        payment_reference: paymentData.paymentReference,
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating loan payment:', error);
      return { success: false, error: 'Failed to create payment record' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in createLoanPayment:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Create payment transaction record
export async function createPaymentTransaction(
  loanPaymentId: string,
  transactionData: {
    paystackReference: string;
    amount: number;
    email: string;
    accessCode?: string;
    transactionId?: string;
  }
): Promise<{ success: boolean; data?: PaymentTransaction; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('payment_transactions')
      .insert({
        loan_payment_id: loanPaymentId,
        paystack_reference: transactionData.paystackReference,
        paystack_transaction_id: transactionData.transactionId,
        paystack_access_code: transactionData.accessCode,
        amount: transactionData.amount,
        email: transactionData.email,
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating payment transaction:', error);
      return { success: false, error: 'Failed to create transaction record' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in createPaymentTransaction:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Update payment status
// Get payment by reference
export async function getPaymentByReference(
  reference: string
): Promise<{ success: boolean; data?: LoanPayment; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('loan_payments')
      .select('*')
      .eq('payment_reference', reference)
      .single();

    if (error) {
      console.error('Error fetching payment by reference:', error);
      return { success: false, error: 'Failed to fetch payment' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in getPaymentByReference:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

export async function updatePaymentStatus(
  paymentId: string,
  status: 'completed' | 'failed' | 'cancelled',
  verificationData?: PaystackVerifyResponse['data']
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error: paymentError } = await supabase
      .from('loan_payments')
      .update({
        status,
        payment_date: status === 'completed' ? new Date().toISOString() : undefined
      })
      .eq('id', paymentId);

    if (paymentError) {
      console.error('Error updating payment status:', paymentError);
      return { success: false, error: 'Failed to update payment status' };
    }

    // Update transaction record if verification data is provided
    if (verificationData) {
      const { error: transactionError } = await supabase
        .from('payment_transactions')
        .update({
          status: status === 'completed' ? 'success' : 'failed',
          gateway_response: verificationData.gateway_response,
          authorization_code: verificationData.authorization?.authorization_code,
          card_type: verificationData.authorization?.card_type,
          bank: verificationData.authorization?.bank,
          last4: verificationData.authorization?.last4,
          paid_at: status === 'completed' ? new Date().toISOString() : undefined
        })
        .eq('paystack_reference', verificationData.reference);

      if (transactionError) {
        console.error('Error updating transaction status:', transactionError);
        // Don't fail the whole operation for this
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updatePaymentStatus:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Update payment status by reference (convenience function)
export async function updatePaymentStatusByReference(
  reference: string,
  status: 'completed' | 'failed' | 'cancelled',
  verificationData?: PaystackVerifyResponse['data']
): Promise<{ success: boolean; error?: string }> {
  try {
    // First get the payment by reference
    const paymentResult = await getPaymentByReference(reference);
    
    if (!paymentResult.success || !paymentResult.data) {
      return { success: false, error: 'Payment not found' };
    }

    const payment = paymentResult.data;

    // Update the payment status
    const updateResult = await updatePaymentStatus(payment.id, status, verificationData);
    
    if (!updateResult.success) {
      return updateResult;
    }

    // Update payment schedule if this was a scheduled payment and payment is completed
    if (status === 'completed' && payment.scheduled_payment_month) {
      const scheduleUpdateResult = await updatePaymentScheduleStatus(
        payment.loan_application_id,
        payment.scheduled_payment_month,
        {
          status: 'paid',
          actualPaymentAmount: payment.payment_amount,
          actualPrincipal: payment.principal_amount,
          actualInterest: payment.interest_amount,
          actualProcessingFee: payment.processing_fee,
          paidDate: new Date().toISOString()
        }
      );

      if (!scheduleUpdateResult.success) {
        console.error('Failed to update payment schedule:', scheduleUpdateResult.error);
        // Don't fail the whole operation, just log the error
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updatePaymentStatusByReference:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Update payment schedule status
export async function updatePaymentScheduleStatus(
  loanApplicationId: string,
  monthNumber: number,
  updateData: {
    status: 'paid' | 'partially_paid' | 'overdue' | 'pending';
    actualPaymentAmount?: number;
    actualPrincipal?: number;
    actualInterest?: number;
    actualProcessingFee?: number;
    paidDate?: string;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('payment_schedules')
      .update({
        status: updateData.status,
        actual_payment_amount: updateData.actualPaymentAmount || 0,
        actual_principal: updateData.actualPrincipal || 0,
        actual_interest: updateData.actualInterest || 0,
        actual_processing_fee: updateData.actualProcessingFee || 0,
        paid_date: updateData.paidDate,
        updated_at: new Date().toISOString()
      })
      .eq('loan_application_id', loanApplicationId)
      .eq('month_number', monthNumber);

    if (error) {
      console.error('Error updating payment schedule:', error);
      return { success: false, error: 'Failed to update payment schedule' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updatePaymentScheduleStatus:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Get loan payment history
export async function getLoanPaymentHistory(
  loanApplicationId: string
): Promise<{ success: boolean; data?: LoanPayment[]; error?: string }> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('loan_payments')
      .select('*')
      .eq('loan_application_id', loanApplicationId)
      .eq('borrower_id', user.id)
      .order('payment_date', { ascending: false });

    if (error) {
      console.error('Error fetching payment history:', error);
      return { success: false, error: 'Failed to fetch payment history' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in getLoanPaymentHistory:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Get payment schedule for a loan
export async function getPaymentSchedule(
  loanApplicationId: string
): Promise<{ success: boolean; data?: PaymentSchedule[]; error?: string }> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('payment_schedules')
      .select('*')
      .eq('loan_application_id', loanApplicationId)
      .order('month_number', { ascending: true });

    if (error) {
      console.error('Error fetching payment schedule:', error);
      return { success: false, error: 'Failed to fetch payment schedule' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in getPaymentSchedule:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Generate payment schedule for approved loan
export async function generatePaymentSchedule(
  loanApplicationId: string,
  principal: number,
  interestRate: number,
  durationMonths: number,
  processingFeeRate: number,
  startDate?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // First try the stored procedure
    const { error: rpcError } = await supabase.rpc('generate_payment_schedule', {
      p_loan_application_id: loanApplicationId,
      p_principal: principal,
      p_interest_rate: interestRate,
      p_duration_months: durationMonths,
      p_processing_fee_rate: processingFeeRate,
      p_start_date: startDate || new Date().toISOString()
    });

    if (!rpcError) {
      return { success: true };
    }

    console.warn('Stored procedure failed, creating schedule manually:', rpcError);
    
    // Fallback: Create payment schedule records manually
    return await createPaymentScheduleManually(
      loanApplicationId,
      principal,
      interestRate,
      durationMonths,
      processingFeeRate,
      startDate
    );
  } catch (error) {
    console.error('Error in generatePaymentSchedule:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Manual payment schedule creation (fallback)
export async function createPaymentScheduleManually(
  loanApplicationId: string,
  principal: number,
  interestRate: number,
  durationMonths: number,
  processingFeeRate: number,
  startDate?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const monthlyRate = interestRate / 100 / 12;
    const processingFee = principal * (processingFeeRate / 100);
    
    // Calculate monthly payment using loan formula
    let monthlyPayment: number;
    if (monthlyRate === 0) {
      monthlyPayment = principal / durationMonths;
    } else {
      monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, durationMonths)) / 
                      (Math.pow(1 + monthlyRate, durationMonths) - 1);
    }
    
    if (!isFinite(monthlyPayment) || monthlyPayment <= 0) {
      monthlyPayment = principal / durationMonths;
    }
    
    let remainingBalance = principal;
    const scheduleStart = new Date(startDate || new Date().toISOString());
    const scheduleRecords = [];
    
    for (let month = 1; month <= durationMonths; month++) {
      const dueDate = new Date(scheduleStart);
      dueDate.setMonth(dueDate.getMonth() + month);
      
      const interestAmount = monthlyRate === 0 ? 0 : remainingBalance * monthlyRate;
      const principalAmount = monthlyPayment - interestAmount;
      remainingBalance = Math.max(0, remainingBalance - principalAmount);
      
      // Handle final payment rounding
      const isLastMonth = month === durationMonths;
      const adjustedPrincipal = isLastMonth ? principalAmount + remainingBalance : principalAmount;
      if (isLastMonth) remainingBalance = 0;
      
      const monthProcessingFee = month === 1 ? processingFee : 0;
      const totalMonthlyPayment = monthlyPayment + monthProcessingFee;
      
      scheduleRecords.push({
        loan_application_id: loanApplicationId,
        month_number: month,
        due_date: dueDate.toISOString(),
        expected_payment_amount: totalMonthlyPayment,
        expected_principal: adjustedPrincipal,
        expected_interest: interestAmount,
        expected_processing_fee: monthProcessingFee,
        expected_balance: remainingBalance,
        status: 'pending' as const
      });
    }
    
    // Insert all schedule records
    const { error } = await supabase
      .from('payment_schedules')
      .insert(scheduleRecords);
    
    if (error) {
      console.error('Error creating payment schedule manually:', error);
      return { success: false, error: 'Failed to create payment schedule' };
    }
    
    console.log(`Created ${scheduleRecords.length} payment schedule records for loan ${loanApplicationId}`);
    return { success: true };
  } catch (error) {
    console.error('Error in createPaymentScheduleManually:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Get payment summary for a loan
export async function getLoanPaymentSummary(
  loanApplicationId: string
): Promise<{ 
  success: boolean; 
  data?: {
    totalExpected: number;
    totalPaid: number;
    totalOutstanding: number;
    nextPaymentDue: string | null;
    nextPaymentAmount: number | null;
    overdueAmount: number;
    paymentsRemaining: number;
  }; 
  error?: string;
}> {
  try {
    const { data, error } = await supabase.rpc('get_loan_payment_summary', {
      p_loan_application_id: loanApplicationId
    });

    if (error) {
      console.error('Error fetching payment summary:', error);
      return { success: false, error: 'Failed to fetch payment summary' };
    }

    const summary = data?.[0];
    if (!summary) {
      return { success: false, error: 'No payment summary found' };
    }

    return { 
      success: true, 
      data: {
        totalExpected: parseFloat(summary.total_expected || '0'),
        totalPaid: parseFloat(summary.total_paid || '0'),
        totalOutstanding: parseFloat(summary.total_outstanding || '0'),
        nextPaymentDue: summary.next_payment_due,
        nextPaymentAmount: parseFloat(summary.next_payment_amount || '0'),
        overdueAmount: parseFloat(summary.overdue_amount || '0'),
        paymentsRemaining: summary.payments_remaining || 0
      }
    };
  } catch (error) {
    console.error('Error in getLoanPaymentSummary:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Calculate payment breakdown for a specific amount
export function calculatePaymentBreakdown(
  paymentAmount: number,
  outstandingBalance: number,
  interestRate: number,
  processingFee: number = 0
): {
  principalAmount: number;
  interestAmount: number;
  processingFee: number;
  totalAmount: number;
} {
  const monthlyRate = interestRate / 100 / 12;
  const interestAmount = outstandingBalance * monthlyRate;
  const principalAmount = Math.max(0, paymentAmount - interestAmount - processingFee);
  
  return {
    principalAmount,
    interestAmount,
    processingFee,
    totalAmount: principalAmount + interestAmount + processingFee
  };
}

// Get next payment due for a loan
export async function getNextPaymentDue(
  loanApplicationId: string
): Promise<{ success: boolean; data?: PaymentSchedule; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('payment_schedules')
      .select('*')
      .eq('loan_application_id', loanApplicationId)
      .eq('status', 'pending')
      .order('due_date', { ascending: true })
      .limit(1)
      .single();

    if (error) {
      console.error('Error fetching next payment due:', error);
      return { success: false, error: 'Failed to fetch next payment due' };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Error in getNextPaymentDue:', error);
    return { success: false, error: 'An unexpected error occurred' };
  }
}

// Helper function to format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

// Helper function to format date
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-NG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

const paymentsLib = {
  initializePaystackPayment,
  verifyPaystackPayment,
  createLoanPayment,
  createPaymentTransaction,
  updatePaymentStatus,
  getLoanPaymentHistory,
  getPaymentSchedule,
  generatePaymentSchedule,
  getLoanPaymentSummary,
  calculatePaymentBreakdown,
  getNextPaymentDue,
  formatCurrency,
  formatDate
};

export default paymentsLib; 