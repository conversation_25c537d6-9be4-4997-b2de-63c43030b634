"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { getUserLoanApplications, withdrawApplication } from '@/lib/loan-applications';
import { getPaymentSchedule, getLoanPaymentSummary, generatePaymentSchedule, type PaymentSchedule } from '@/lib/payments';
import { Database } from '@/lib/supabase';
import { toast } from 'sonner';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  FileText,
  Trash2,
  User,
  Calendar,
  CreditCard,
  DollarSign,
  TrendingUp,
  Cal<PERSON>tor,
  History
} from 'lucide-react';
import PaymentModal from '@/components/payments/PaymentModal';
import TransactionHistory from '@/components/payments/TransactionHistory';

type LoanApplication = Database['public']['Tables']['loan_applications']['Row'] & {
  loan_offers?: {
    product_name: string;
    interest_rate: number;
    rate_type: string;
    processing_fee: number;
    profiles?: {
      individual_accounts?: Array<{ full_name: string }>;
      corporate_accounts?: Array<{ organization_name: string }>;
    };
  };
  lender_name?: string;
};

// For now, approved applications are considered active loans
// In production, these will be fetched from a separate loans table after disbursement

type PaymentSummary = {
  totalExpected: number;
  totalPaid: number;
  totalOutstanding: number;
  nextPaymentDue: string | null;
  nextPaymentAmount: number | null;
  overdueAmount: number;
  paymentsRemaining: number;
};

const LoanDetailsModal = ({ loan }: { loan: LoanApplication }) => {
  const [paymentSummary, setPaymentSummary] = useState<PaymentSummary | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showTransactionHistory, setShowTransactionHistory] = useState(false);

  const fetchPaymentSummary = useCallback(async () => {
    try {
      const result = await getLoanPaymentSummary(loan.id);
      if (result.success && result.data) {
        setPaymentSummary(result.data);
      }
    } catch (error) {
      console.error('Error fetching payment summary:', error);
    }
  }, [loan.id]);

  useEffect(() => {
    if (loan.status === 'approved') {
      fetchPaymentSummary();
    }
  }, [loan.status, loan.id, fetchPaymentSummary]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const processingFee = loan.requested_amount * ((loan.loan_offers?.processing_fee || 0) / 100);
  const monthlyRate = (loan.loan_offers?.interest_rate || 0) / 100 / 12;
  const monthlyPayment = loan.requested_amount * (monthlyRate * Math.pow(1 + monthlyRate, loan.requested_duration)) / (Math.pow(1 + monthlyRate, loan.requested_duration) - 1);

  return (
    <div className="space-y-6">
      {/* Loan Overview */}
      <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-green-800">Active Loan Details</h3>
          <Badge className="bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-green-600 font-medium">Loan Product</p>
            <p className="text-green-800 font-semibold">{loan.loan_offers?.product_name || 'Personal Loan'}</p>
          </div>
          <div>
            <p className="text-green-600 font-medium">Lender</p>
            <p className="text-green-800 font-semibold">{loan.lender_name || 'Financial Institution'}</p>
          </div>
        </div>
      </div>

      {/* Payment Summary for Active Loans */}
      {paymentSummary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-600">Total Paid</p>
                  <p className="text-xl font-bold text-blue-800">{formatCurrency(paymentSummary.totalPaid)}</p>
                </div>
                <CheckCircle className="w-6 h-6 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-orange-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-orange-600">Outstanding</p>
                  <p className="text-xl font-bold text-orange-800">{formatCurrency(paymentSummary.totalOutstanding)}</p>
                </div>
                <DollarSign className="w-6 h-6 text-orange-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-red-200">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-red-600">Next Payment</p>
                  <p className="text-xl font-bold text-red-800">
                    {paymentSummary.nextPaymentAmount ? formatCurrency(paymentSummary.nextPaymentAmount) : 'None'}
                  </p>
                </div>
                <Calendar className="w-6 h-6 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Financial Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              Loan Terms
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Principal Amount:</span>
              <span className="font-semibold">{formatCurrency(loan.requested_amount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Interest Rate:</span>
              <span className="font-semibold">{loan.loan_offers?.interest_rate}% {loan.loan_offers?.rate_type}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Processing Fee:</span>
              <span className="font-semibold text-orange-600">
                {formatCurrency(processingFee)} ({loan.loan_offers?.processing_fee}%)
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Loan Duration:</span>
              <span className="font-semibold">{loan.requested_duration} months</span>
            </div>
            <div className="border-t pt-2 mt-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Monthly Payment:</span>
                <span className="font-semibold text-blue-600">{formatCurrency(monthlyPayment)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Important Dates
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Application Date:</span>
              <span className="font-semibold">{formatDate(loan.application_date)}</span>
            </div>
            {loan.approval_date && (
              <div className="flex justify-between">
                <span className="text-gray-600">Approval Date:</span>
                <span className="font-semibold">{formatDate(loan.approval_date)}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">Disbursement Date:</span>
              <span className="font-semibold">{formatDate(loan.approval_date || loan.created_at)}</span>
            </div>
            {paymentSummary?.nextPaymentDue && (
              <div className="border-t pt-2 mt-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Next Payment Due:</span>
                  <span className="font-semibold text-red-600">
                    {formatDate(paymentSummary.nextPaymentDue)}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Borrower Information */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <User className="w-4 h-4" />
            Borrower Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-3">
              <div>
                <p className="text-gray-600">Full Name:</p>
                <p className="font-semibold">{loan.full_name}</p>
              </div>
              <div>
                <p className="text-gray-600">Email:</p>
                <p className="font-semibold">{loan.email}</p>
              </div>
              <div>
                <p className="text-gray-600">Phone Number:</p>
                <p className="font-semibold">{loan.phone_number || 'Not provided'}</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <p className="text-gray-600">Employment Status:</p>
                <p className="font-semibold capitalize">{loan.employment_status.replace('_', ' ')}</p>
              </div>
              <div>
                <p className="text-gray-600">Monthly Income:</p>
                <p className="font-semibold">{loan.monthly_income ? formatCurrency(loan.monthly_income) : 'Not disclosed'}</p>
              </div>
              <div>
                <p className="text-gray-600">Loan Purpose:</p>
                <p className="font-semibold">{loan.purpose}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Guarantor Information */}
      {loan.guarantor_name && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <User className="w-4 h-4" />
              Guarantor Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div>
                  <p className="text-gray-600">Guarantor Name:</p>
                  <p className="font-semibold">{loan.guarantor_name}</p>
                </div>
                <div>
                  <p className="text-gray-600">Relationship:</p>
                  <p className="font-semibold">{loan.guarantor_relationship || 'Not specified'}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-gray-600">Phone Number:</p>
                  <p className="font-semibold">{loan.guarantor_phone || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-gray-600">Email:</p>
                  <p className="font-semibold">{loan.guarantor_email || 'Not provided'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <div className="flex gap-3 pt-4 border-t">
        <Button 
          className="flex-1" 
          variant="default"
          onClick={() => setShowPaymentModal(true)}
        >
          <CreditCard className="w-4 h-4 mr-2" />
          Make Payment
        </Button>
        <Button 
          className="flex-1" 
          variant="outline"
          onClick={() => setShowTransactionHistory(true)}
        >
          <History className="w-4 h-4 mr-2" />
          Transaction History
        </Button>
        <Button className="flex-1" variant="outline">
          <FileText className="w-4 h-4 mr-2" />
          Download Statement
        </Button>
        <Button className="flex-1" variant="outline">
          <AlertCircle className="w-4 h-4 mr-2" />
          Contact Support
        </Button>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        loan={loan}
        onPaymentSuccess={() => {
          setShowPaymentModal(false);
          fetchPaymentSummary();
          toast.success('Payment initiated successfully!');
        }}
      />

      {/* Transaction History Modal */}
      <Dialog open={showTransactionHistory} onOpenChange={setShowTransactionHistory}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Transaction History</DialogTitle>
          </DialogHeader>
          <TransactionHistory 
            loanApplicationId={loan.id}
            onRefresh={fetchPaymentSummary}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

const RepaymentPlanModal = ({ loan, onRefresh }: { loan: LoanApplication; onRefresh?: () => void }) => {
  const [paymentSchedule, setPaymentSchedule] = useState<PaymentSchedule[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [usingCalculatedSchedule, setUsingCalculatedSchedule] = useState(false);

  const generateCalculatedSchedule = useCallback((): PaymentSchedule[] => {
    const schedule: PaymentSchedule[] = [];
    const principal = loan.requested_amount;
    const annualRate = (loan.loan_offers?.interest_rate || 0) / 100;
    const monthlyRate = annualRate / 12;
    const months = loan.requested_duration;
    const processingFeeRate = (loan.loan_offers?.processing_fee || 0) / 100;
    const processingFee = principal * processingFeeRate;
    
    // Calculate monthly payment using loan formula
    let monthlyPayment: number;
    if (monthlyRate === 0) {
      // If no interest, just divide principal by months
      monthlyPayment = principal / months;
    } else {
      monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, months)) / (Math.pow(1 + monthlyRate, months) - 1);
    }
    
    // Handle invalid calculations
    if (!isFinite(monthlyPayment) || monthlyPayment <= 0) {
      monthlyPayment = principal / months;
    }
    
    let remainingBalance = principal;
    const startDate = new Date(loan.approval_date || loan.created_at);
    
    for (let month = 1; month <= months; month++) {
      const dueDate = new Date(startDate);
      dueDate.setMonth(dueDate.getMonth() + month);
      
      const interestAmount = monthlyRate === 0 ? 0 : remainingBalance * monthlyRate;
      const principalAmount = monthlyPayment - interestAmount;
      remainingBalance = Math.max(0, remainingBalance - principalAmount);
      
      // Handle final payment rounding
      const isLastMonth = month === months;
      const adjustedPrincipal = isLastMonth ? principalAmount + remainingBalance : principalAmount;
      if (isLastMonth) remainingBalance = 0;
      
      const monthProcessingFee = month === 1 ? processingFee : 0;
      const totalMonthlyPayment = monthlyPayment + monthProcessingFee;
      
      schedule.push({
        id: `calc-${loan.id}-${month}`,
        loan_application_id: loan.id,
        month_number: month,
        due_date: dueDate.toISOString(),
        expected_payment_amount: totalMonthlyPayment,
        expected_principal: adjustedPrincipal,
        expected_interest: interestAmount,
        expected_processing_fee: monthProcessingFee,
        expected_balance: remainingBalance,
        actual_payment_amount: 0,
        actual_principal: 0,
        actual_interest: 0,
        actual_processing_fee: 0,
        actual_balance: null,
        status: 'pending' as const,
        paid_date: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });
    }
    
    return schedule;
  }, [loan.id, loan.requested_amount, loan.requested_duration, loan.loan_offers?.interest_rate, loan.loan_offers?.processing_fee, loan.approval_date, loan.created_at]);

  const fetchPaymentSchedule = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await getPaymentSchedule(loan.id);
      if (result.success && result.data && result.data.length > 0) {
        setPaymentSchedule(result.data);
        setUsingCalculatedSchedule(false);
      } else {
        // Try to generate actual payment schedule in database for approved loans
        if (loan.status === 'approved' && loan.loan_offers?.interest_rate && loan.loan_offers?.processing_fee) {
          console.log('Creating payment schedule for approved loan:', loan.id);
          const generateResult = await generatePaymentSchedule(
            loan.id,
            loan.requested_amount,
            loan.loan_offers.interest_rate,
            loan.requested_duration,
            loan.loan_offers.processing_fee,
            loan.approval_date || loan.created_at
          );
          
          if (generateResult.success) {
            // Fetch the newly created schedule
            const newScheduleResult = await getPaymentSchedule(loan.id);
            if (newScheduleResult.success && newScheduleResult.data) {
              setPaymentSchedule(newScheduleResult.data);
              setUsingCalculatedSchedule(false);
              return;
            }
          }
        }
        
        // Fallback to calculated schedule if database generation fails
        const calculatedSchedule = generateCalculatedSchedule();
        setPaymentSchedule(calculatedSchedule);
        setUsingCalculatedSchedule(true);
      }
    } catch (error) {
      console.error('Error fetching payment schedule:', error);
      // Fallback to calculated schedule on error
      const calculatedSchedule = generateCalculatedSchedule();
      setPaymentSchedule(calculatedSchedule);
      setUsingCalculatedSchedule(true);
    } finally {
      setIsLoading(false);
    }
  }, [loan.id, loan.status, loan.requested_amount, loan.requested_duration, loan.loan_offers?.interest_rate, loan.loan_offers?.processing_fee, loan.approval_date, loan.created_at, generateCalculatedSchedule]);

  useEffect(() => {
    fetchPaymentSchedule();
  }, [fetchPaymentSchedule]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Calculate totals from actual payment schedule if available, otherwise use calculated values
  const totalExpected = paymentSchedule.reduce((sum, item) => sum + item.expected_payment_amount, 0);
  const totalPaid = paymentSchedule.reduce((sum, item) => sum + item.actual_payment_amount, 0);
  const processingFeeTotal = paymentSchedule.reduce((sum, item) => sum + item.expected_processing_fee, 0);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Loan Summary */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-3">Loan Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-gray-500">Principal Amount</p>
            <p className="font-semibold">{formatCurrency(loan.requested_amount)}</p>
          </div>
          <div>
            <p className="text-gray-500">Interest Rate</p>
            <p className="font-semibold">{loan.loan_offers?.interest_rate}% per annum</p>
          </div>
          <div>
            <p className="text-gray-500">Loan Duration</p>
            <p className="font-semibold">{loan.requested_duration} months</p>
          </div>
          <div>
            <p className="text-gray-500">Processing Fee</p>
            <p className="font-semibold text-orange-600">
              {formatCurrency(processingFeeTotal)} ({loan.loan_offers?.processing_fee || 0}%)
            </p>
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-3 text-sm">
          <div>
            <p className="text-gray-500">Total Expected</p>
            <p className="font-semibold">{formatCurrency(totalExpected)}</p>
          </div>
          <div>
            <p className="text-gray-500">Total Paid</p>
            <p className="font-semibold text-green-600">{formatCurrency(totalPaid)}</p>
          </div>
          <div>
            <p className="text-gray-500">Outstanding</p>
            <p className="font-semibold text-red-600">{formatCurrency(totalExpected - totalPaid)}</p>
          </div>
        </div>
      </div>

      {/* Enhanced Repayment Schedule Table */}
      <div>
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-semibold">Monthly Repayment Schedule</h3>
          {usingCalculatedSchedule && (
            <Badge variant="outline" className="text-orange-600 border-orange-200">
              Calculated Schedule
            </Badge>
          )}
        </div>
        <div className="border rounded-lg overflow-hidden">
          <div className="max-h-96 overflow-y-auto">
            <table className="w-full text-sm">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th className="px-3 py-2 text-left">Month</th>
                  <th className="px-3 py-2 text-right">Expected</th>
                  <th className="px-3 py-2 text-right">Actual</th>
                  <th className="px-3 py-2 text-right">Principal</th>
                  <th className="px-3 py-2 text-right">Interest</th>
                  <th className="px-3 py-2 text-right">Balance</th>
                  <th className="px-3 py-2 text-center">Status</th>
                </tr>
              </thead>
              <tbody>
                {paymentSchedule.map((item, index) => (
                  <tr key={item.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-3 py-2">{item.month_number}</td>
                    <td className="px-3 py-2 text-right font-medium">
                      {formatCurrency(item.expected_payment_amount)}
                    </td>
                    <td className="px-3 py-2 text-right font-medium">
                      {item.actual_payment_amount > 0 ? (
                        <span className="text-green-600">{formatCurrency(item.actual_payment_amount)}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-3 py-2 text-right">
                      {formatCurrency(item.actual_principal > 0 ? item.actual_principal : item.expected_principal)}
                    </td>
                    <td className="px-3 py-2 text-right">
                      {formatCurrency(item.actual_interest > 0 ? item.actual_interest : item.expected_interest)}
                    </td>
                    <td className="px-3 py-2 text-right">
                      {formatCurrency(item.actual_balance || item.expected_balance)}
                    </td>
                    <td className="px-3 py-2 text-center">
                      <Badge 
                        className={
                          item.status === 'paid' ? 'bg-green-100 text-green-800' :
                          item.status === 'partially_paid' ? 'bg-yellow-100 text-yellow-800' :
                          item.status === 'overdue' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }
                      >
                        {item.status === 'paid' ? 'Paid' :
                         item.status === 'partially_paid' ? 'Partial' :
                         item.status === 'overdue' ? 'Overdue' :
                         'Pending'}
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

                {/* Payment Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Payment Information</h4>
            <div className="space-y-2 text-sm text-blue-700">
              {usingCalculatedSchedule && (
                <p className="bg-orange-50 border border-orange-200 rounded p-2 mb-2">
                  <strong>Note:</strong> This schedule is calculated based on your loan terms. Actual payment tracking will be updated once you make payments.
                </p>
              )}
              <p>
                <strong>Regular Payment Schedule:</strong> Monthly payments are due on the same date each month.
              </p>
              <p>
                <strong>Early Payments:</strong> You can make additional payments at any time to reduce interest costs.
              </p>
              <p>
                <strong>Payment Methods:</strong> Secure online payments through Paystack support cards, bank transfers, and mobile money.
              </p>
            </div>
            
            {/* Refresh Button */}
            <div className="mt-3 pt-3 border-t border-blue-200">
              <Button
                variant="outline"
                size="sm"
                onClick={async () => {
                  console.log('Manual refresh triggered for loan:', loan.id);
                  await fetchPaymentSchedule();
                  onRefresh?.(); // Trigger parent refresh
                  toast.success('Payment schedule refreshed');
                }}
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <History className="w-4 h-4 mr-2" />
                Refresh Schedule
              </Button>
              
              {/* Debug info */}
              <div className="mt-2 text-xs text-blue-600">
                Schedule Type: {usingCalculatedSchedule ? 'Calculated' : 'Database'} | 
                Loan Status: {loan.status} | 
                Records: {paymentSchedule.length}
              </div>
            </div>
          </div>
    </div>
  );
};

export default function MyLoansPage() {
  const [applications, setApplications] = useState<LoanApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [withdrawingId, setWithdrawingId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('applications');
  const [selectedLoanForPlan, setSelectedLoanForPlan] = useState<LoanApplication | null>(null);
  const [selectedLoanForDetails, setSelectedLoanForDetails] = useState<LoanApplication | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState<{ loan: LoanApplication | null; isOpen: boolean }>({ loan: null, isOpen: false });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  useEffect(() => {
    fetchApplications();
  }, []);

  // Function to trigger refresh of payment-related data
  const triggerRefresh = useCallback(() => {
    setRefreshTrigger(prev => prev + 1);
    fetchApplications(); // Refresh the main application data
  }, []);

  const fetchApplications = async () => {
    try {
      setIsLoading(true);
      const result = await getUserLoanApplications();
      
      if (result.success) {
        const applications = Array.isArray(result.data) ? result.data : [];
        setApplications(applications);
      } else {
        toast.error(result.error || 'Failed to fetch applications');
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('An error occurred while fetching applications');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWithdraw = async (applicationId: string) => {
    if (confirm('Are you sure you want to withdraw this application? This action cannot be undone.')) {
      try {
        setWithdrawingId(applicationId);
        const result = await withdrawApplication(applicationId);
        
        if (result.success) {
          setApplications(prev => prev.map(app => 
            app.id === applicationId ? { ...app, status: 'withdrawn' } : app
          ));
          toast.success('Application withdrawn successfully');
        } else {
          toast.error(result.error || 'Failed to withdraw application');
        }
      } catch (error) {
        console.error('Error withdrawing application:', error);
        toast.error('An error occurred while withdrawing application');
      } finally {
        setWithdrawingId(null);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      case 'withdrawn':
        return <AlertCircle className="w-4 h-4" />;
      case 'active':
        return <TrendingUp className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Separate active loans (approved) from applications
  const activeLoans = applications.filter(app => app.status === 'approved');
  const pendingApplications = applications.filter(app => app.status !== 'approved');
  
  // Calculate summary statistics
  const pendingCount = pendingApplications.filter(app => app.status === 'pending').length;
  const approvedCount = activeLoans.length;
  const totalApplicationAmount = pendingApplications
    .filter(app => app.status === 'pending')
    .reduce((sum, app) => sum + app.requested_amount, 0);
  const totalActiveAmount = activeLoans
    .reduce((sum, app) => sum + app.requested_amount, 0);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">My Loans</h1>
          <p className="text-gray-500 text-sm mt-1">
            Track your loan applications and manage active loans
          </p>
        </div>
        <Button 
          onClick={() => window.location.href = '/dashboard/borrower/marketplace'}
          className="bg-blue-600 hover:bg-blue-700"
        >
          Apply for New Loan
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Pending Applications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingCount}</div>
            <p className="text-sm text-gray-500">
              {formatCurrency(totalApplicationAmount)}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Active Loans
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{approvedCount}</div>
            <p className="text-sm text-gray-500">
              {formatCurrency(totalActiveAmount)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for Applications and Active Loans */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="applications">
            Loan Applications ({pendingApplications.length})
          </TabsTrigger>
          <TabsTrigger value="active">
            Active Loans ({activeLoans.length})
          </TabsTrigger>
        </TabsList>

        {/* Applications Tab */}
        <TabsContent value="applications">
          {pendingApplications.length === 0 ? (
            <Card className="text-center p-8">
              <CardContent>
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Applications Yet</h3>
                <p className="text-gray-500 mb-4">
                  You haven&apos;t submitted any loan applications. Browse the marketplace to find suitable offers.
                </p>
                <Button 
                  onClick={() => window.location.href = '/dashboard/borrower/marketplace'}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Browse Marketplace
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {pendingApplications.map((application) => (
                <Card key={application.id} className="hover:shadow-sm transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-base font-semibold truncate">
                            {application.loan_offers?.product_name || 'Loan Application'}
                          </h3>
                          <Badge 
                            className={`${getStatusColor(application.status)} flex items-center gap-1 text-xs px-2 py-0.5`}
                          >
                            {getStatusIcon(application.status)}
                            {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500">
                          {application.lender_name || 'Lender'} • {formatDate(application.created_at)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                      <div className="flex items-center gap-1.5">
                        <DollarSign className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Amount</p>
                          <p className="text-sm font-medium truncate">
                            {formatCurrency(application.requested_amount)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Duration</p>
                          <p className="text-sm font-medium">
                            {application.requested_duration}mo
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1.5">
                        <CreditCard className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Rate</p>
                          <p className="text-sm font-medium truncate">
                            {application.loan_offers?.interest_rate}% {application.loan_offers?.rate_type?.slice(0, 2)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1.5">
                        <User className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Guarantor</p>
                          <p className="text-sm font-medium truncate">
                            {application.guarantor_name || 'None'}
                          </p>
                        </div>
                      </div>
                    </div>

                    {application.status === 'rejected' && application.rejection_reason && (
                      <div className="bg-red-50 border border-red-200 rounded p-2 mb-3">
                        <p className="text-xs text-red-700">
                          <strong>Rejected:</strong> {application.rejection_reason}
                        </p>
                      </div>
                    )}

                    <div className="flex justify-between items-center text-xs">
                      <span className="text-gray-400">
                        ID: {application.id.slice(0, 8)}...
                      </span>
                      
                      {application.status === 'pending' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleWithdraw(application.id)}
                          disabled={withdrawingId === application.id}
                          className="text-red-600 border-red-200 hover:bg-red-50 h-7 px-2 text-xs"
                        >
                          {withdrawingId === application.id ? (
                            <div className="animate-spin rounded-full h-3 w-3 border border-red-600 border-t-transparent"></div>
                          ) : (
                            <>
                              <Trash2 className="w-3 h-3 mr-1" />
                              Withdraw
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Active Loans Tab */}
        <TabsContent value="active">
          {activeLoans.length === 0 ? (
            <Card className="text-center p-6">
              <CardContent className="py-4">
                <TrendingUp className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <h3 className="text-base font-semibold text-gray-600 mb-2">No Active Loans</h3>
                <p className="text-sm text-gray-500 mb-2">
                  Approved and disbursed loans will appear here for tracking.
                </p>
                <div className="text-xs text-gray-400">
                  View repayment schedules, payment history, and progress.
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {activeLoans.map((loan) => (
                <Card key={loan.id} className="hover:shadow-sm transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="text-base font-semibold truncate">
                            {loan.loan_offers?.product_name || 'Active Loan'}
                          </h3>
                          <Badge className="bg-green-100 text-green-800 flex items-center gap-1 text-xs px-2 py-0.5">
                            <CheckCircle className="w-3 h-3" />
                            Active
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500">
                          {loan.lender_name || 'Lender'} • {formatDate(loan.created_at)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
                      <div className="flex items-center gap-1.5">
                        <DollarSign className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Principal</p>
                          <p className="text-sm font-medium truncate">
                            {formatCurrency(loan.requested_amount)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Duration</p>
                          <p className="text-sm font-medium">
                            {loan.requested_duration}mo
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1.5">
                        <CreditCard className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Rate</p>
                          <p className="text-sm font-medium truncate">
                            {loan.loan_offers?.interest_rate}% {loan.loan_offers?.rate_type?.slice(0, 2)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1.5">
                        <TrendingUp className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                        <div className="min-w-0">
                          <p className="text-xs text-gray-500">Status</p>
                          <p className="text-sm font-medium text-green-600">
                            Disbursed
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between items-center text-xs">
                      <span className="text-gray-400">
                        Loan ID: {loan.id.slice(0, 8)}...
                      </span>
                      <div className="flex gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 px-2 text-xs"
                              onClick={() => setSelectedLoanForDetails(loan)}
                            >
                              <FileText className="w-3 h-3 mr-1" />
                              View Details
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Loan Details</DialogTitle>
                            </DialogHeader>
                            {selectedLoanForDetails && (
                              <LoanDetailsModal loan={selectedLoanForDetails} />
                            )}
                          </DialogContent>
                        </Dialog>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 px-2 text-xs"
                              onClick={() => setSelectedLoanForPlan(loan)}
                            >
                              <Calculator className="w-3 h-3 mr-1" />
                              Repayment Plan
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                            <DialogHeader>
                              <DialogTitle>Loan Repayment Schedule</DialogTitle>
                            </DialogHeader>
                            {selectedLoanForPlan && (
                              <RepaymentPlanModal 
                                loan={selectedLoanForPlan} 
                                onRefresh={triggerRefresh}
                                key={`${selectedLoanForPlan.id}-${refreshTrigger}`}
                              />
                            )}
                          </DialogContent>
                        </Dialog>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 px-2 text-xs bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                          onClick={() => setShowPaymentModal({ loan, isOpen: true })}
                        >
                          <CreditCard className="w-3 h-3 mr-1" />
                          Make Payment
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Global Payment Modal */}
      {showPaymentModal.loan && (
        <PaymentModal
          isOpen={showPaymentModal.isOpen}
          onClose={() => setShowPaymentModal({ loan: null, isOpen: false })}
          loan={showPaymentModal.loan}
          onPaymentSuccess={() => {
            setShowPaymentModal({ loan: null, isOpen: false });
            triggerRefresh(); // This will refresh both applications and payment schedules
            toast.success('Payment initiated successfully!');
          }}
        />
      )}
    </div>
  );
} 