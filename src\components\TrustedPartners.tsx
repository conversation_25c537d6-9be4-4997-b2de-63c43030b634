"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';

const TrustedPartners = () => {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);

  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => {
      window.removeEventListener('scroll', toggleVisibility);
    };
  }, []);

  const handleRedirect = () => {
    router.push('/404');
  };

  return (
    <div className="bg-white py-12 sm:py-16 relative">
      <div className="max-w-4xl mx-auto flex flex-col md:flex-row items-center text-center md:text-left px-4">
        <div className="w-full md:w-1/2">
          <div className="relative h-96 flex items-center justify-center">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-gray-300 rounded-full" style={{ width: '300px', height: '290px' }}></div>
            </div>
            <div className="relative grid grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded-none shadow-lg flex items-center">
                <Image src="/image 4.svg" alt="Hedgelife" width={40} height={40} />
                <span className="ml-2 font-bold">HEDGELIFE</span>
              </div>
              <div className="bg-white p-4 rounded-none shadow-lg flex items-center">
                <Image src="/image 4.svg" alt="Hedgelife" width={40} height={40} />
                <span className="ml-2 font-bold">HEDGELIFE</span>
              </div>
              <div className="bg-white p-4 rounded-none shadow-lg flex items-center">
                <Image src="/image 4.svg" alt="Hedgelife" width={40} height={40} />
                <span className="ml-2 font-bold">HEDGELIFE</span>
              </div>
              <div className="bg-white p-4 rounded-none shadow-lg flex items-center">
                <Image src="/image 4.svg" alt="Hedgelife" width={40} height={40} />
                <span className="ml-2 font-bold">HEDGELIFE</span>
              </div>
              <div className="col-span-2 flex justify-center">
                <div className="bg-white p-4 rounded-none shadow-lg flex items-center">
                  <Image src="/image 4.svg" alt="Hedgelife" width={60} height={40} />
                  <span className="ml-2 font-bold">HEDGELIFE</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full md:w-1/2 mt-8 md:mt-0 md:pl-12">
          <h3 className="text-lg font-semibold text-gray-600">Let&apos;s See Our</h3>
          <h2 className="text-4xl font-bold mt-2">Trusted Partners</h2>
          <p className="text-gray-500 mt-4">
            We&apos;re committed to making our clients successful by becoming their partners and trusted advisors. Kredxa believes in being your trusted partner and earning that trust through confidence and performance in service and support.
          </p>
          <Button
            className="mt-6 bg-black text-white rounded-full px-8 py-3"
            onClick={handleRedirect}
          >
            JOIN WITH US
          </Button>
        </div>
      </div>
      {isVisible && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-10 right-10 bg-black text-white rounded-full p-3 shadow-lg hover:bg-gray-800 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7-7-7 7" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default TrustedPartners;