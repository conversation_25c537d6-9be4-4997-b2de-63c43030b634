"use client"

import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, TrendingUp, FileText, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogOverlay } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { <PERSON>anOffer } from "@/data/mockLenders"

interface Review {
  rating: number;
  comment: string;
  borrower: string;
  date: string;
}

interface LoanDetailsPopupProps {
  offer: LoanOffer
  onClose: () => void
}

export default function LoanDetailsPopup({ offer, onClose }: LoanDetailsPopupProps) {
  if (!offer) return null

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        style={{
          width: "16px",
          height: "16px",
          fill: i < rating ? "#facc15" : "transparent",
          color: i < rating ? "#facc15" : "#d1d5db",
        }}
      />
    ))
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogOverlay className="fixed inset-0 bg-black/50 z-50" />
      <DialogContent className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-[700px] max-h-[90vh] bg-white rounded-[24px] p-0 border-none shadow-2xl z-51 overflow-hidden">
        <div className="h-full max-h-[90vh] overflow-y-auto p-8 pb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <span className="text-3xl">{offer.lenderIcon}</span>
              <div>
                <h2 className="text-2xl font-bold text-[#1a1a1a] m-0 leading-tight">
                  {offer.productName}
                </h2>
                <p className="text-sm text-gray-600">by {offer.lenderName}</p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="icon"
              className="p-2 bg-transparent border-none cursor-pointer rounded-lg flex items-center justify-center"
            >
              <X className="w-6 h-6 text-gray-500" />
            </Button>
          </div>

          {/* Lender Rating */}
          <div className="flex items-center gap-3 mb-6">
            <div className="flex gap-1">{renderStars(offer.rating)}</div>
            <span className="text-sm font-medium text-[#1a1a1a]">{offer.rating}</span>
            <span className="text-sm text-gray-500">({offer.reviewCount} reviews)</span>
          </div>

          {/* Key Loan Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#1a1a1a]">Loan Terms</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Amount Range</span>
                  <span className="text-sm font-semibold">₦{offer.minAmount} - ₦{offer.maxAmount}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Duration</span>
                  <span className="text-sm font-semibold">{offer.minDuration}-{offer.maxDuration} months</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Interest Rate</span>
                  <span className="text-sm font-semibold">{offer.interestRate} {offer.rateType}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Processing Fee</span>
                  <span className="text-sm font-semibold">{offer.processingFee}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Collateral Required</span>
                  <span className="text-sm font-semibold">{offer.collateralRequired ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-[#1a1a1a]">Performance</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-blue-600" />
                  <span className="text-sm text-gray-600">Total Applications</span>
                  <span className="text-sm font-semibold ml-auto">{offer.totalApplications}</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-gray-600">Total Disbursed</span>
                  <span className="text-sm font-semibold ml-auto">{offer.totalDisbursed}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-purple-600" />
                  <span className="text-sm text-gray-600">Approval Time</span>
                  <span className="text-sm font-semibold ml-auto">{offer.approvalTime}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-gray-600">Success Rate</span>
                  <span className="text-sm font-semibold ml-auto">{offer.successRate}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Target Borrowers */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[#1a1a1a] mb-3">Target Borrowers</h3>
            <div className="flex flex-wrap gap-2">
              {offer.targetBorrowers.map((borrower: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {borrower}
                </Badge>
              ))}
            </div>
          </div>

          {/* Description */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[#1a1a1a] mb-3">About This Loan</h3>
            <p className="text-sm text-gray-700 leading-relaxed">{offer.description}</p>
          </div>

          {/* Requirements */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[#1a1a1a] mb-3">Required Documents</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {offer.documents.map((doc: string, index: number) => (
                <div key={index} className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-700">{doc}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Process */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[#1a1a1a] mb-3">Application Process</h3>
            <div className="space-y-2">
              {offer.process.map((step: string, index: number) => (
                <div key={index} className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-blue-600 text-xs font-bold">{index + 1}</span>
                  </div>
                  <span className="text-sm text-gray-700">{step}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Reviews */}
          {offer.reviews && offer.reviews.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-[#1a1a1a] mb-3">Recent Reviews</h3>
              <div className="space-y-3">
                {offer.reviews.slice(0, 2).map((review: Review, index: number) => (
                  <div key={index} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex gap-1">{renderStars(review.rating)}</div>
                      <span className="text-xs text-gray-500">{review.date}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-1">{review.comment}</p>
                    <span className="text-xs text-gray-500">- {review.borrower}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Contact Information */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-[#1a1a1a] mb-3">Contact Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Email:</span>
                <span className="font-medium">{offer.contactInfo.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Phone:</span>
                <span className="font-medium">{offer.contactInfo.phone}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Address:</span>
                <span className="font-medium">{offer.contactInfo.address}</span>
              </div>
              {offer.contactInfo.website && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Website:</span>
                  <span className="font-medium text-blue-600">{offer.contactInfo.website}</span>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button 
              onClick={onClose}
              variant="outline"
              className="flex-1"
            >
              Close
            </Button>
            <Button 
              className="flex-1 bg-[#2D0A0A] hover:bg-[#1a0808] text-white"
            >
              Apply for This Loan
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
