-- Add foreign key relationship between loan_offers.lender_id and profiles.id
-- This allows for proper joins between the tables

-- First, ensure all existing loan_offers have valid lender_ids that exist in profiles
-- Remove any orphaned loan offers (shouldn't be any in a new system)
DELETE FROM loan_offers 
WHERE lender_id NOT IN (SELECT id FROM profiles);

-- Add the foreign key constraint
ALTER TABLE loan_offers 
ADD CONSTRAINT fk_loan_offers_lender_profile 
FOREIGN KEY (lender_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Add an index for better performance on joins
CREATE INDEX IF NOT EXISTS idx_loan_offers_lender_profile ON loan_offers(lender_id);

-- Update RLS policy to ensure proper access through the relationship
DROP POLICY IF EXISTS "Public can view active loan offers" ON loan_offers;

CREATE POLICY "Public can view active loan offers" ON loan_offers 
FOR SELECT USING (
  is_active = true 
  AND status = 'active'
  AND EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = loan_offers.lender_id
  )
); 