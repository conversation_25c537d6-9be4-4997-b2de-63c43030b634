import * as React from "react";
import { cn } from "../../lib/utils";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * The visual style of the button.
   * @default "default"
   */
  variant?: "default" | "outline" | "secondary" | "ghost" | "link";
  /**
   * The size of the button.
   * @default "default"
   */
  size?: "default" | "sm" | "lg" | "icon";
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "default", ...props }, ref) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
          {
            // Variant Styles
            "bg-black text-white hover:bg-gray-800": variant === "default", // Adjusted from marketplace default for specific black/white
            "border border-gray-300 bg-white text-black hover:bg-gray-100": variant === "outline", // Retained marketplace outline
            "bg-secondary text-secondary-foreground hover:bg-secondary/80": variant === "secondary", // From dev
            "text-gray-700 hover:bg-gray-100 hover:text-gray-900": variant === "ghost", // Adjusted from marketplace ghost
            "text-primary underline-offset-4 hover:underline": variant === "link", // From dev
          },
          {
            // Size Styles
            "h-10 px-4 py-2": size === "default", // Combined from marketplace and dev
            "h-9 rounded-md px-3": size === "sm", // From dev
            "h-11 rounded-md px-8": size === "lg", // From dev
            "h-10 w-10 p-0": size === "icon", // Combined from marketplace and dev
          },
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";