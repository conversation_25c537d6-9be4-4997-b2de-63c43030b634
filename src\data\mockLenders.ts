export interface LoanOffer {
  id: string;
  productName: string;
  lenderName: string;
  lenderType: "Individual Lender" | "Corporate Lender";
  lenderIcon: string;
  minAmount: string;
  maxAmount: string;
  minDuration: string;
  maxDuration: string;
  interestRate: string;
  rateType: string;
  processingFee: string;
  collateralRequired: boolean;
  targetBorrowers: string[];
  description: string;
  rating: number;
  reviewCount: number;
  totalApplications: number;
  totalDisbursed: string;
  status: 'active' | 'paused' | 'draft';
  createdAt: string;
  // Additional marketplace display fields
  documents: string[];
  approvalTime: string;
  successRate: string;
  averageResponseTime: string;
  requirements: string[];
  process: string[];
  contactInfo: {
    email: string;
    phone: string;
    address: string;
    website?: string;
  };
  reviews: {
    rating: number;
    comment: string;
    date: string;
    borrower: string;
  }[];
}

export const loanOffers: { individual: LoanOffer[]; corporate: LoanOffer[] } = {
  individual: [
    {
      id: "IND-001",
      productName: "Personal Growth Loan",
      lenderName: "<PERSON>",
      lenderType: "Individual Lender",
      lenderIcon: "👨‍💼",
      minAmount: "100,000",
      maxAmount: "2,000,000",
      minDuration: "6",
      maxDuration: "24",
      interestRate: "3.5",
      rateType: "% Monthly",
      processingFee: "2.0",
      collateralRequired: false,
      targetBorrowers: ["Individual borrowers", "Small business owners"],
      description: "Experienced individual lender with over 5 years of experience in personal and small business financing. Committed to helping borrowers achieve their financial goals through flexible loan terms and personalized service.",
      rating: 4.8,
      reviewCount: 45,
      totalApplications: 45,
      totalDisbursed: "₦25,000,000",
      status: 'active',
      createdAt: '2024-01-15',
      documents: ["Valid government-issued ID", "Proof of income", "Bank statements (3 months)", "Utility bill for address verification"],
      approvalTime: "2 hours",
      successRate: "92%",
      averageResponseTime: "2 hours",
      requirements: [
        "Valid government-issued ID",
        "Proof of income",
        "Bank statements (3 months)",
        "Utility bill for address verification",
        "Employment letter or business registration"
      ],
      process: [
        "Submit loan application",
        "Document verification",
        "Credit check",
        "Loan approval",
        "Fund disbursement"
      ],
      contactInfo: {
        email: "<EMAIL>",
        phone: "+234 ************",
        address: "Lagos, Nigeria",
        website: "www.johnsmithlending.com"
      },
      reviews: [
        {
          rating: 5,
          comment: "Very professional and quick response. The loan process was smooth and transparent.",
          date: "2024-02-15",
          borrower: "Sarah M."
        },
        {
          rating: 4,
          comment: "Good experience overall. Interest rates are competitive.",
          date: "2024-02-10",
          borrower: "Michael K."
        },
        {
          rating: 5,
          comment: "Excellent service! Would definitely recommend.",
          date: "2024-02-05",
          borrower: "David O."
        }
      ]
    },
    {
      id: "IND-002",
      productName: "Emergency Quick Loan",
      lenderName: "Sarah Johnson",
      lenderType: "Individual Lender",
      lenderIcon: "👩‍💼",
      minAmount: "50,000",
      maxAmount: "1,500,000",
      minDuration: "3",
      maxDuration: "12",
      interestRate: "4.2",
      rateType: "% Monthly",
      processingFee: "1.5",
      collateralRequired: false,
      targetBorrowers: ["Individual borrowers", "Freelancers"],
      description: "Dedicated to providing quick and accessible loans for emergency situations and salary advances. Focus on helping individuals manage unexpected expenses and cash flow needs.",
      rating: 4.9,
      reviewCount: 78,
      totalApplications: 78,
      totalDisbursed: "₦45,000,000",
      status: 'active',
      createdAt: '2024-01-10',
      documents: ["Valid ID", "Salary account statement", "Employment letter", "Recent payslip"],
      approvalTime: "1 hour",
      successRate: "95%",
      averageResponseTime: "1 hour",
      requirements: [
        "Valid ID",
        "Salary account statement",
        "Employment letter",
        "Recent payslip",
        "Bank verification"
      ],
      process: [
        "Quick application",
        "Salary verification",
        "Instant approval",
        "Same day disbursement"
      ],
      contactInfo: {
        email: "<EMAIL>",
        phone: "+234 ************",
        address: "Abuja, Nigeria"
      },
      reviews: [
        {
          rating: 5,
          comment: "Life saver! Got my emergency loan within hours.",
          date: "2024-02-18",
          borrower: "James P."
        },
        {
          rating: 5,
          comment: "Best lender for salary advances. Very reliable.",
          date: "2024-02-12",
          borrower: "Linda R."
        }
      ]
    }
  ],
  corporate: [
    {
      id: "CORP-001",
      productName: "Business Expansion Loan",
      lenderName: "First Bank",
      lenderType: "Corporate Lender",
      lenderIcon: "🏦",
      minAmount: "1,000,000",
      maxAmount: "50,000,000",
      minDuration: "12",
      maxDuration: "48",
      interestRate: "2.8",
      rateType: "% Monthly",
      processingFee: "1.0",
      collateralRequired: true,
      targetBorrowers: ["Established businesses", "Small business owners"],
      description: "Leading financial institution with extensive experience in corporate and business financing. Committed to supporting business growth and development through comprehensive financial solutions.",
      rating: 4.9,
      reviewCount: 1200,
      totalApplications: 1200,
      totalDisbursed: "₦500,000,000",
      status: 'active',
      createdAt: '2024-01-05',
      documents: ["Business registration", "Financial statements", "Tax clearance", "Bank statements", "Collateral documents"],
      approvalTime: "24 hours",
      successRate: "95%",
      averageResponseTime: "24 hours",
      requirements: [
        "Business registration certificate",
        "Financial statements (2 years)",
        "Tax clearance certificate",
        "Bank statements (6 months)",
        "Collateral documentation",
        "Business plan"
      ],
      process: [
        "Application submission",
        "Business verification",
        "Financial assessment",
        "Collateral evaluation",
        "Credit committee review",
        "Approval and disbursement"
      ],
      contactInfo: {
        email: "<EMAIL>",
        phone: "+*********** 7890",
        address: "Lagos, Nigeria",
        website: "www.firstbank.com"
      },
      reviews: [
        {
          rating: 5,
          comment: "Excellent business loan experience. Professional and efficient.",
          date: "2024-02-20",
          borrower: "Tech Solutions Ltd"
        },
        {
          rating: 4,
          comment: "Good terms and professional service. Recommended for business loans.",
          date: "2024-02-15",
          borrower: "Green Foods Co"
        }
      ]
    },
    {
      id: "CORP-002",
      productName: "SME Growth Capital",
      lenderName: "Access Bank",
      lenderType: "Corporate Lender",
      lenderIcon: "🏦",
      minAmount: "500,000",
      maxAmount: "25,000,000",
      minDuration: "6",
      maxDuration: "36",
      interestRate: "3.2",
      rateType: "% Monthly",
      processingFee: "1.2",
      collateralRequired: false,
      targetBorrowers: ["Small business owners", "Startups"],
      description: "Specialized in SME financing with flexible terms and quick approval processes. Supporting small and medium enterprises in their growth journey.",
      rating: 4.7,
      reviewCount: 850,
      totalApplications: 850,
      totalDisbursed: "₦300,000,000",
      status: 'active',
      createdAt: '2024-01-12',
      documents: ["Business registration", "Financial statements", "Bank statements", "Business plan"],
      approvalTime: "48 hours",
      successRate: "93%",
      averageResponseTime: "48 hours",
      requirements: [
        "Business registration",
        "Financial statements (1 year)",
        "Bank statements (3 months)",
        "Business plan",
        "Personal guarantee"
      ],
      process: [
        "Application and documentation",
        "Business assessment",
        "Financial review",
        "Risk evaluation",
        "Approval and funding"
      ],
      contactInfo: {
        email: "<EMAIL>",
        phone: "+234 ************",
        address: "Lagos, Nigeria",
        website: "www.accessbank.com"
      },
      reviews: [
        {
          rating: 5,
          comment: "Perfect for our startup. Quick approval and great terms.",
          date: "2024-02-18",
          borrower: "Innovate Tech"
        },
        {
          rating: 4,
          comment: "Good SME loan provider. Process was straightforward.",
          date: "2024-02-10",
          borrower: "Local Restaurant"
        }
      ]
    }
  ]
};

// Legacy interface for backward compatibility
export interface Lender {
  id: string;
  name: string;
  type: "Individual Lender" | "Corporate Lender";
  rating: number;
  totalLoans: number;
  activeLoans: number;
  totalAmount: string;
  minAmount: string;
  maxAmount: string;
  interestRate: string;
  specialties: string[];
  icon: string;
  about: string;
  successRate: string;
  averageResponseTime: string;
  requirements: string[];
  process: string[];
  contactInfo: {
    email: string;
    phone: string;
    address: string;
    website?: string;
  };
  reviews: {
    rating: number;
    comment: string;
    date: string;
    borrower: string;
  }[];
  loanHistory: {
    year: number;
    totalLoans: number;
    totalAmount: string;
    successRate: string;
  }[];
}

// Legacy export for backward compatibility
export const lenders = {
  individual: loanOffers.individual.map(offer => ({
    id: offer.id,
    name: offer.lenderName,
    type: offer.lenderType,
    rating: offer.rating,
    totalLoans: offer.totalApplications,
    activeLoans: offer.totalApplications,
    totalAmount: offer.totalDisbursed,
    minAmount: `₦${offer.minAmount}`,
    maxAmount: `₦${offer.maxAmount}`,
    interestRate: `${offer.interestRate} ${offer.rateType}`,
    specialties: offer.targetBorrowers,
    icon: offer.lenderIcon,
    about: offer.description,
    successRate: offer.successRate,
    averageResponseTime: offer.averageResponseTime,
    requirements: offer.requirements,
    process: offer.process,
    contactInfo: offer.contactInfo,
    reviews: offer.reviews,
    loanHistory: [
      {
        year: 2023,
        totalLoans: offer.totalApplications,
        totalAmount: offer.totalDisbursed,
        successRate: offer.successRate
      }
    ]
  })),
  corporate: loanOffers.corporate.map(offer => ({
    id: offer.id,
    name: offer.lenderName,
    type: offer.lenderType,
    rating: offer.rating,
    totalLoans: offer.totalApplications,
    activeLoans: offer.totalApplications,
    totalAmount: offer.totalDisbursed,
    minAmount: `₦${offer.minAmount}`,
    maxAmount: `₦${offer.maxAmount}`,
    interestRate: `${offer.interestRate} ${offer.rateType}`,
    specialties: offer.targetBorrowers,
    icon: offer.lenderIcon,
    about: offer.description,
    successRate: offer.successRate,
    averageResponseTime: offer.averageResponseTime,
    requirements: offer.requirements,
    process: offer.process,
    contactInfo: offer.contactInfo,
    reviews: offer.reviews,
    loanHistory: [
      {
        year: 2023,
        totalLoans: offer.totalApplications,
        totalAmount: offer.totalDisbursed,
        successRate: offer.successRate
      }
    ]
  }))
}; 