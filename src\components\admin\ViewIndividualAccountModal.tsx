"use client";

import React from 'react';
import { IndividualAccount } from '@/lib/types';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface Props {
  account: IndividualAccount | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function ViewIndividualAccountModal({ account, isOpen, onClose }: Props) {
  if (!account) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>View Individual Account</DialogTitle>
        </DialogHeader>
        <div className="mt-4 space-y-4">
          <div>
            <h3 className="font-medium">Personal Information</h3>
            <div className="text-sm mt-2 space-y-1">
              <p><strong>Full Name:</strong> {account.full_name}</p>
              <p><strong>Email:</strong> {account.email}</p>
              <p><strong>Phone:</strong> {account.phone_number}</p>
              <p><strong>BVN:</strong> {account.bvn}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium">Employment Details</h3>
            <div className="text-sm mt-2 space-y-1">
              <p><strong>Employer:</strong> {account.employer}</p>
              <p><strong>Position:</strong> {account.position}</p>
              <p><strong>Monthly Income:</strong> ₦{account.monthly_income?.toLocaleString()}</p>
              <p><strong>Employment Type:</strong> {account.employment_type}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium">Status</h3>
            <div className="text-sm mt-2 space-y-1">
                <p><strong>Verification Status:</strong> {account.verification_status}</p>
                <p><strong>Account Status:</strong> {account.is_active ? 'Active' : 'Disabled'}</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 