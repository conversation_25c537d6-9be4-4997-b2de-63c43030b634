"use client";
import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';

const LendAndBorrow = () => {
  const router = useRouter();

  const handleRedirect = () => {
    router.push('/404');
  };

  return (
    <div className="bg-white p-4 sm:p-8 rounded-lg" style={{border: '1px solid #E5E7EB'}}>
      <div className="text-center">
        <h2 className="text-sm font-semibold text-gray-500">The Smarter Way</h2>
        <h1 className="text-3xl sm:text-4xl font-bold">Lend and Borrow</h1>
        <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-sm sm:text-base">
          Africa&apos;s First Lending and Borrowing Marketplace with Affordable and Competitive Interest Rate
        </p>
        <div className="mt-6 flex justify-center">
          <div className="inline-flex flex-col sm:flex-row">
            <Button className="rounded-full sm:rounded-l-full sm:rounded mb-2 sm:mb-0 bg-[#0D253F] text-white px-6 py-3 text-base sm:text-lg font-bold hover:bg-[#0D253F]/90" onClick={handleRedirect}>LEND</Button>
            <Button className="rounded-full sm:rounded-r-full sm:rounded-l-none bg-[#D9E8DB] text-black px-6 py-3 text-base sm:text-lg font-bold hover:bg-[#D9E8DB]/90" onClick={handleRedirect}>BORROW</Button>
          </div>
        </div>
      </div>
      <div className="mt-8 flex justify-center sm:justify-end">
        <a href="#" className="text-gray-600 hover:underline">View More →</a>
      </div>
      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-8">
        <Card className="rounded-2xl shadow-lg">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Personal Loan</CardTitle>
              <Badge className="bg-[#D9EFDF] text-black">Best Rate</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between text-sm text-gray-500">
              <span>Amount</span>
              <span className="font-semibold text-black">Up to ₦50,000</span>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>Interest Rate</span>
              <span className="font-semibold text-black">8.999% p.a.</span>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>Tenure</span>
              <span className="font-semibold text-black">12-60 months</span>
            </div>
            <Button className="w-full mt-6 bg-black text-white rounded-full py-3" onClick={handleRedirect}>Apply Now</Button>
          </CardContent>
        </Card>
        <Card className="rounded-2xl shadow-lg">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Business Loan</CardTitle>
              <Badge className="bg-[#F5F5F5] text-black">Fast Approval</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between text-sm text-gray-500">
              <span>Amount</span>
              <span className="font-semibold text-black">Up to ₦200,000</span>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>Interest Rate</span>
              <span className="font-semibold text-black">10.99% p.a.</span>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>Tenure</span>
              <span className="font-semibold text-black">24-84 months</span>
            </div>
            <Button className="w-full mt-6 bg-black text-white rounded-full py-3" onClick={handleRedirect}>Apply Now</Button>
          </CardContent>
        </Card>
        <Card className="rounded-2xl shadow-lg">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Home Loan</CardTitle>
              <Badge className="bg-[#F5F2CE] text-black">Low EMI</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between text-sm text-gray-500">
              <span>Amount</span>
              <span className="font-semibold text-black">Up to ₦500,000</span>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>Interest Rate</span>
              <span className="font-semibold text-black">6.99% p.a.</span>
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>Tenure</span>
              <span className="font-.semibold text-black">Up to 30 years</span>
            </div>
            <Button className="w-full mt-6 bg-black text-white rounded-full py-3" onClick={handleRedirect}>Apply Now</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LendAndBorrow;