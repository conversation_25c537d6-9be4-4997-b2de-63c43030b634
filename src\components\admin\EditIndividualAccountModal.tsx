"use client";

import React, { useState, useEffect } from 'react';
import { IndividualAccount } from '@/lib/types';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { updateUserProfile } from '@/lib/auth-supabase';

interface Props {
  account: IndividualAccount | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

export default function EditIndividualAccountModal({ account, isOpen, onClose, onSave }: Props) {
  const [formData, setFormData] = useState<Partial<IndividualAccount>>({});
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (account) {
      setFormData(account);
    }
  }, [account]);

  if (!account) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSave = async () => {
    setIsSaving(true);
    const result = await updateUserProfile(account.user_id, {
        full_name: formData.full_name,
        phone_number: formData.phone_number,
        bvn: formData.bvn,
        employer: formData.employer,
        position: formData.position,
        monthly_income: formData.monthly_income ? Number(formData.monthly_income) : null,
    });
    if (result.success) {
      alert("Account updated successfully");
      onSave();
      onClose();
    } else {
      alert(`Failed to update account: ${result.error}`);
    }
    setIsSaving(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Individual Account</DialogTitle>
        </DialogHeader>
        <div className="mt-4 grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="full_name" className="text-right">Full Name</Label>
                <Input id="full_name" value={formData.full_name || ''} onChange={handleChange} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone_number" className="text-right">Phone</Label>
                <Input id="phone_number" value={formData.phone_number || ''} onChange={handleChange} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="bvn" className="text-right">BVN</Label>
                <Input id="bvn" value={formData.bvn || ''} onChange={handleChange} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="employer" className="text-right">Employer</Label>
                <Input id="employer" value={formData.employer || ''} onChange={handleChange} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="position" className="text-right">Position</Label>
                <Input id="position" value={formData.position || ''} onChange={handleChange} className="col-span-3" />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="monthly_income" className="text-right">Income</Label>
                <Input id="monthly_income" type="number" value={formData.monthly_income || ''} onChange={handleChange} className="col-span-3" />
            </div>
        </div>
        <DialogFooter>
            <Button variant="outline" onClick={onClose}>Cancel</Button>
            <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 