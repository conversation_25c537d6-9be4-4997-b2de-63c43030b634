"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Calendar,
  Download,
  Filter,
  BarChart3,
  <PERSON><PERSON>hart,
  FileText
} from "lucide-react";

const earningsData = {
  totalEarnings: "₦2,450,000",
  thisMonth: "₦180,000",
  lastMonth: "₦165,000",
  growth: 9.1,
  totalLoans: 24,
  activeLoans: 18,
  completedLoans: 6,
  averageInterestRate: 4.2
};

const monthlyEarnings = [
  { month: "Jan", earnings: 120000 },
  { month: "Feb", earnings: 135000 },
  { month: "Mar", earnings: 145000 },
  { month: "Apr", earnings: 155000 },
  { month: "May", earnings: 165000 },
  { month: "Jun", earnings: 180000 }
];

const loanPerformance = [
  { status: "Performing", count: 18, amount: "₦1,800,000", percentage: 75 },
  { status: "Overdue", count: 4, amount: "₦400,000", percentage: 17 },
  { status: "Defaulted", count: 2, amount: "₦250,000", percentage: 8 }
];

export default function ReportsAndEarningsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("6months");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reports & Earnings</h1>
          <p className="text-gray-600 mt-1">Comprehensive analytics and financial insights</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            Filter
          </Button>
          <Button className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Period Selector */}
      <div className="flex gap-2 bg-gray-100 p-1 rounded-lg w-fit">
        {[
          { value: "1month", label: "1 Month" },
          { value: "3months", label: "3 Months" },
          { value: "6months", label: "6 Months" },
          { value: "1year", label: "1 Year" }
        ].map((period) => (
          <button
            key={period.value}
            onClick={() => setSelectedPeriod(period.value)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedPeriod === period.value
                ? "bg-white text-gray-900 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            {period.label}
          </button>
        ))}
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold">{earningsData.totalEarnings}</p>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">+{earningsData.growth}%</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">This Month</p>
                <p className="text-2xl font-bold">{earningsData.thisMonth}</p>
                <div className="flex items-center gap-1 mt-1">
                  <TrendingUp className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600">+9.1% vs last month</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Loans</p>
                <p className="text-2xl font-bold">{earningsData.activeLoans}</p>
                <p className="text-sm text-gray-500 mt-1">Out of {earningsData.totalLoans} total</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg. Interest Rate</p>
                <p className="text-2xl font-bold">{earningsData.averageInterestRate}%</p>
                <p className="text-sm text-gray-500 mt-1">Across all loans</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Earnings Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Earnings Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-2xl font-bold">{earningsData.thisMonth}</p>
                  <p className="text-sm text-gray-600">This month&apos;s earnings</p>
                </div>
                <Badge className="bg-green-100 text-green-800">
                  +{earningsData.growth}% vs last month
                </Badge>
              </div>
              
              {/* Simple bar chart */}
              <div className="space-y-2">
                {monthlyEarnings.map((item) => (
                  <div key={item.month} className="flex items-center gap-3">
                    <span className="text-sm font-medium w-8">{item.month}</span>
                    <div className="flex-1 bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                        style={{ width: `${(item.earnings / 200000) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium w-20 text-right">₦{(item.earnings / 1000).toFixed(0)}K</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Loan Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Loan Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loanPerformance.map((item) => (
                <div key={item.status} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 rounded-full" style={{
                      backgroundColor: item.status === "Performing" ? "#10B981" : 
                                     item.status === "Overdue" ? "#F59E0B" : "#EF4444"
                    }}></div>
                    <span className="font-medium">{item.status}</span>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{item.count} loans</p>
                    <p className="text-sm text-gray-600">{item.amount}</p>
                  </div>
                </div>
              ))}
              
              <div className="mt-4 pt-4 border-t">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Portfolio</span>
                  <span className="font-bold">₦2,450,000</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports Section */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="flex items-center gap-2 p-4 h-auto">
              <FileText className="w-5 h-5" />
              <div className="text-left">
                <p className="font-medium">Earnings Report</p>
                <p className="text-sm text-gray-600">Detailed breakdown of all earnings</p>
              </div>
            </Button>
            
            <Button variant="outline" className="flex items-center gap-2 p-4 h-auto">
              <Users className="w-5 h-5" />
              <div className="text-left">
                <p className="font-medium">Borrower Analysis</p>
                <p className="text-sm text-gray-600">Performance by borrower</p>
              </div>
            </Button>
            
            <Button variant="outline" className="flex items-center gap-2 p-4 h-auto">
              <BarChart3 className="w-5 h-5" />
              <div className="text-left">
                <p className="font-medium">Portfolio Health</p>
                <p className="text-sm text-gray-600">Risk assessment & metrics</p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 