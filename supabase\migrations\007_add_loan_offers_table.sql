-- Add loan offers table
CREATE TABLE loan_offers (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  lender_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  product_name TEXT NOT NULL,
  min_amount DECIMAL(15,2) NOT NULL,
  max_amount DECIMAL(15,2) NOT NULL,
  min_duration INTEGER NOT NULL, -- in months
  max_duration INTEGER NOT NULL, -- in months
  interest_rate DECIMAL(5,2) NOT NULL,
  rate_type TEXT CHECK (rate_type IN ('% Monthly', '% Annually')) NOT NULL,
  processing_fee DECIMAL(5,2) DEFAULT 0,
  collateral_required BOOLEAN DEFAULT false,
  description TEXT,
  target_borrowers TEXT[], -- array of target borrower types
  status TEXT CHECK (status IN ('draft', 'active', 'paused', 'expired')) DEFAULT 'active',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_loan_offers_lender_id ON loan_offers(lender_id);
CREATE INDEX idx_loan_offers_status ON loan_offers(status);
CREATE INDEX idx_loan_offers_is_active ON loan_offers(is_active);
CREATE INDEX idx_loan_offers_created_at ON loan_offers(created_at);

-- Add updated_at trigger
CREATE TRIGGER update_loan_offers_updated_at 
BEFORE UPDATE ON loan_offers 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE loan_offers ENABLE ROW LEVEL SECURITY;

-- RLS policies for loan offers
CREATE POLICY "Lenders can view own loan offers" ON loan_offers FOR SELECT USING (auth.uid() = lender_id);
CREATE POLICY "Lenders can insert own loan offers" ON loan_offers FOR INSERT WITH CHECK (auth.uid() = lender_id);
CREATE POLICY "Lenders can update own loan offers" ON loan_offers FOR UPDATE USING (auth.uid() = lender_id);
CREATE POLICY "Lenders can delete own loan offers" ON loan_offers FOR DELETE USING (auth.uid() = lender_id);

-- Public can view active loan offers (for marketplace)
CREATE POLICY "Public can view active loan offers" ON loan_offers FOR SELECT USING (is_active = true AND status = 'active'); 