"use client";
import React, { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { getCurrentUser, getUserProfile } from "@/lib/auth-supabase";
import { getActiveMarketplaceOffers } from "@/lib/loan-offers";
import { Database } from "@/lib/supabase";
import { Loader2 } from "lucide-react";

type LoanOffer = Database['public']['Tables']['loan_offers']['Row'];

interface EnhancedLoanOffer extends LoanOffer {
  lender_name?: string;
  lender_type?: string;
}

export default function BorrowerDashboard() {
  const [displayName, setDisplayName] = useState<string>("there");
  const [loading, setLoading] = useState(true);
  const [recommendations, setRecommendations] = useState<EnhancedLoanOffer[]>([]);
  const [recommendationsLoading, setRecommendationsLoading] = useState(true);

  const fetchRecommendations = useCallback(async () => {
    try {
      setRecommendationsLoading(true);
      const result = await getActiveMarketplaceOffers();
      if (result.success) {
        // For now, we'll just get the top 6 offers with highest ratings
        // Later, this can be replaced with a proper recommendation algorithm
        const sortedOffers = (result.data || [])
          .sort((a, b) => b.interest_rate - a.interest_rate) // Sort by interest rate for now
          .slice(0, 6);
        setRecommendations(sortedOffers);
      }
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    } finally {
      setRecommendationsLoading(false);
    }
  }, []);

  useEffect(() => {
    async function fetchUser() {
      setLoading(true);
      const user = await getCurrentUser();
      if (user) {
        const profile = await getUserProfile(user.id);
        if (profile) {
          if (profile.user_type === "individual") {
            setDisplayName(profile.full_name || user.email || "there");
          } else if (profile.user_type === "corporate") {
            setDisplayName(profile.organization_name || user.email || "there");
          }
        } else {
          setDisplayName(user.email || "there");
        }
      }
      setLoading(false);
    }
    
    fetchUser();
    fetchRecommendations();
  }, [fetchRecommendations]);

  // Transform Supabase loan offers to match UI format
  const transformOfferForUI = (offer: EnhancedLoanOffer) => {
    return {
      id: offer.id,
      lenderName: offer.product_name,
      productName: offer.lender_name || 'Lender',
      lenderIcon: offer.lender_type === 'Corporate Lender' ? "🏦" : "👨‍💼",
      rating: 4.5, // Default rating - would come from reviews table in production
      minAmount: offer.min_amount.toLocaleString(),
      maxAmount: offer.max_amount.toLocaleString(),
      interestRate: offer.interest_rate,
      rateType: offer.rate_type,
      minDuration: offer.min_duration,
      maxDuration: offer.max_duration,
      collateralRequired: offer.collateral_required,
      processingFee: offer.processing_fee,
      targetBorrowers: offer.target_borrowers || []
    };
  };

  // Variables for real data (replace with API/backend data as needed)
  const activeLoansCount = 0; // TODO: Fetch real active loans count
  const activeLoansTotal = 0; // TODO: Fetch real total amount
  const nextPaymentDue = "-"; // TODO: Fetch real next payment due (e.g., '12 Days')
  const nextPaymentAmount = 0; // TODO: Fetch real next payment amount
  const totalRepaid = 0; // TODO: Fetch real total repaid
  const totalRepaidPercent = 0; // TODO: Fetch real percent complete
  const availableOffers = recommendations.length; // Use real count from Supabase

  // Stats array using variables
  const stats = [
    { label: "Active Loans", value: activeLoansCount, sub: `₦${activeLoansTotal.toLocaleString()} total`, icon: "💳" },
    { label: "Next Payment", value: nextPaymentDue, sub: `₦${nextPaymentAmount.toLocaleString()} due`, icon: "📅" },
    { label: "Total Repaid", value: `₦${totalRepaid.toLocaleString()}`, sub: `${totalRepaidPercent}% complete`, icon: "✅" },
    { label: "Available Offers", value: availableOffers, sub: "View Marketplace", icon: "🎁", link: "/dashboard/borrower/marketplace" },
  ];

  // Transform recommendations for UI
  const allRecommendations = recommendations.map(transformOfferForUI);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Card */}
      <div className="flex items-center justify-between bg-white rounded-xl shadow p-6">
        <div>
          <h2 className="text-2xl font-bold mb-1">Welcome back, {displayName}!</h2>
          <p className="text-gray-600 text-sm">
            {availableOffers > 0 
              ? `${availableOffers} loan offers available for you in the marketplace.`
              : "Explore our marketplace to find the perfect loan for your needs."
            }
          </p>
        </div>
        <div className="w-16 h-16 flex items-center justify-center rounded-full border-2 border-gray-200 bg-gray-100 text-3xl font-bold text-gray-700">
          {displayName?.charAt(0)?.toUpperCase() || "?"}
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat) => (
          <div key={stat.label} className="bg-white rounded-xl shadow p-4 flex flex-col items-start">
            <div className="text-2xl mb-2">{stat.icon}</div>
            <div className="text-lg font-semibold">{stat.value}</div>
            <div className="text-xs text-gray-500">{stat.sub}</div>
            {stat.link && (
              <Link href={stat.link} className="text-xs text-black mt-2 hover:underline">
                {stat.sub}
              </Link>
            )}
          </div>
        ))}
      </div>
      
      {/* Upcoming Payments */}
      <div className="bg-white rounded-xl shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Upcoming Payments</h3>
        <div className="flex flex-col items-center justify-center py-8">
          <span className="text-4xl mb-2">📭</span>
          <p className="text-gray-500 text-sm">No Upcoming Payments</p>
        </div>
      </div>
      
      {/* Additional Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mt-4">
        <Link href="/dashboard/borrower/marketplace" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-indigo-600 mb-2">+</span>
          <span className="font-medium text-sm">Apply for New Loan</span>
        </Link>
        <Link href="/dashboard/settings" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-green-600 mb-2">&#128179;</span>
          <span className="font-medium text-sm">Update KYC</span>
        </Link>
        <Link href="/dashboard/borrower/my-loans" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-blue-600 mb-2">&#8635;</span>
          <span className="font-medium text-sm">View Loan History</span>
        </Link>
        <Link href="/dashboard/support" className="flex flex-col items-center justify-center bg-white rounded-xl shadow p-6 cursor-pointer hover:shadow-lg transition">
          <span className="text-3xl text-orange-600 mb-2">&#128222;</span>
          <span className="font-medium text-sm">Contact Support</span>
        </Link>
      </div>
      
      {/* Recommended Offers */}
      <div className="bg-white rounded-xl shadow p-6 mt-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h3 className="text-lg font-semibold">Recommended for You</h3>
            <p className="text-sm text-gray-600 mt-1">Top picks from individual and corporate lenders</p>
          </div>
          <Link href="/dashboard/borrower/marketplace" className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800 text-sm font-medium">
            Go to Marketplace
          </Link>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendationsLoading ? (
            <div className="col-span-full flex justify-center items-center py-12">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                <p className="text-gray-600">Loading recommendations...</p>
              </div>
            </div>
          ) : allRecommendations.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-600 mb-4">No loan offers available at the moment.</p>
              <Link 
                href="/dashboard/borrower/marketplace" 
                className="bg-black text-white px-4 py-2 rounded hover:bg-gray-800 text-sm font-medium"
              >
                Browse Marketplace
              </Link>
            </div>
          ) : (
            allRecommendations.map((offer) => (
              <div key={offer.id} className="bg-white rounded-3xl p-6 shadow-md border border-gray-100 flex flex-col h-fit">
                {/* Lender Header */}
                <div className="flex items-center justify-between mb-6 flex-wrap gap-3">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center overflow-hidden">
                      <span className="text-2xl">{offer.lenderIcon}</span>
                    </div>
                    <div className="min-w-0">
                      <span className="text-xl font-bold text-[#1a1a1a] break-words block">
                        {offer.lenderName}
                      </span>
                      <span className="text-sm text-gray-600 block">
                        {offer.productName}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1.5 flex-shrink-0">
                    <span className="text-yellow-500">★</span>
                    <span className="text-sm font-medium text-[#1a1a1a]">{offer.rating}</span>
                  </div>
                </div>

                {/* Loan Details */}
                <div className="flex flex-col gap-4 mb-6">
                  {[
                    { label: "Loan Amount", value: `₦${offer.minAmount} - ₦${offer.maxAmount}` },
                    { label: "Interest Rate", value: `${offer.interestRate}% ${offer.rateType}` },
                    { label: "Duration", value: `${offer.minDuration}-${offer.maxDuration} months` },
                    { label: "Collateral", value: offer.collateralRequired ? "Required" : "Not Required" },
                    { label: "Processing Fee", value: `${offer.processingFee}%` },
                  ].map((item, index) => (
                    <div key={index} className="flex justify-between items-start gap-4">
                      <span className="text-sm text-gray-600 font-medium">{item.label}</span>
                      <span className="text-sm text-[#1a1a1a] font-semibold text-right">{item.value}</span>
                    </div>
                  ))}
                </div>

                {/* Target Borrowers */}
                <div className="mb-6">
                  <span className="text-sm text-gray-600 font-medium block mb-2">Target Borrowers</span>
                  <div className="flex flex-wrap gap-1">
                    {offer.targetBorrowers.slice(0, 2).map((borrower: string, index: number) => (
                      <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                        {borrower}
                      </span>
                    ))}
                    {offer.targetBorrowers.length > 2 && (
                      <span className="text-xs text-gray-500">+{offer.targetBorrowers.length - 2} more</span>
                    )}
                  </div>
                </div>

                {/* Action Button */}
                <div className="mt-auto">
                  <Link 
                    href="/dashboard/borrower/marketplace" 
                    className="w-full bg-[#2D0A0A] text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-[#1a0808] transition-colors text-center block"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
} 
