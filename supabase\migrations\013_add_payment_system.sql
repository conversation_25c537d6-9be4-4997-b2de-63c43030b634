-- Create payment system tables

-- Create loan_payments table to track individual payments
CREATE TABLE loan_payments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  loan_application_id UUID REFERENCES loan_applications(id) ON DELETE CASCADE NOT NULL,
  borrower_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- Payment details
  payment_amount DECIMAL(15, 2) NOT NULL,
  payment_type TEXT CHECK (payment_type IN ('regular', 'principal_only', 'early_payment', 'late_payment')) DEFAULT 'regular',
  payment_method TEXT CHECK (payment_method IN ('paystack', 'bank_transfer', 'cash', 'check')) DEFAULT 'paystack',
  
  -- Payment breakdown
  principal_amount DECIMAL(15, 2) NOT NULL DEFAULT 0,
  interest_amount DECIMAL(15, 2) NOT NULL DEFAULT 0,
  processing_fee DECIMAL(15, 2) NOT NULL DEFAULT 0,
  late_fee DECIMAL(15, 2) NOT NULL DEFAULT 0,
  
  -- Payment schedule reference
  scheduled_payment_month INTEGER, -- which month this payment is for
  payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  due_date TIMESTAMP WITH TIME ZONE,
  
  -- Status and metadata
  status TEXT CHECK (status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')) DEFAULT 'pending',
  payment_reference TEXT UNIQUE, -- Paystack reference
  notes TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_transactions table for Paystack transaction records
CREATE TABLE payment_transactions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  loan_payment_id UUID REFERENCES loan_payments(id) ON DELETE CASCADE,
  
  -- Paystack transaction details
  paystack_reference TEXT UNIQUE NOT NULL,
  paystack_transaction_id TEXT,
  paystack_access_code TEXT,
  
  -- Transaction details
  amount DECIMAL(15, 2) NOT NULL,
  currency TEXT DEFAULT 'NGN',
  email TEXT NOT NULL,
  
  -- Transaction status
  status TEXT CHECK (status IN ('pending', 'success', 'failed', 'abandoned', 'cancelled')) DEFAULT 'pending',
  gateway_response TEXT,
  
  -- Paystack metadata
  authorization_code TEXT,
  card_type TEXT,
  bank TEXT,
  last4 TEXT,
  
  -- Timestamps
  paid_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment_schedules table to track expected vs actual payments
CREATE TABLE payment_schedules (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  loan_application_id UUID REFERENCES loan_applications(id) ON DELETE CASCADE NOT NULL,
  
  -- Schedule details
  month_number INTEGER NOT NULL,
  due_date TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Expected payment breakdown
  expected_payment_amount DECIMAL(15, 2) NOT NULL,
  expected_principal DECIMAL(15, 2) NOT NULL,
  expected_interest DECIMAL(15, 2) NOT NULL,
  expected_processing_fee DECIMAL(15, 2) NOT NULL DEFAULT 0,
  expected_balance DECIMAL(15, 2) NOT NULL,
  
  -- Actual payment details
  actual_payment_amount DECIMAL(15, 2) DEFAULT 0,
  actual_principal DECIMAL(15, 2) DEFAULT 0,
  actual_interest DECIMAL(15, 2) DEFAULT 0,
  actual_processing_fee DECIMAL(15, 2) DEFAULT 0,
  actual_balance DECIMAL(15, 2),
  
  -- Status
  status TEXT CHECK (status IN ('pending', 'paid', 'overdue', 'partially_paid')) DEFAULT 'pending',
  paid_date TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(loan_application_id, month_number)
);

-- Create indexes for better performance
CREATE INDEX idx_loan_payments_loan_application ON loan_payments(loan_application_id);
CREATE INDEX idx_loan_payments_borrower ON loan_payments(borrower_id);
CREATE INDEX idx_loan_payments_status ON loan_payments(status);
CREATE INDEX idx_loan_payments_payment_date ON loan_payments(payment_date);
CREATE INDEX idx_loan_payments_reference ON loan_payments(payment_reference);

CREATE INDEX idx_payment_transactions_reference ON payment_transactions(paystack_reference);
CREATE INDEX idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX idx_payment_transactions_loan_payment ON payment_transactions(loan_payment_id);

CREATE INDEX idx_payment_schedules_loan_application ON payment_schedules(loan_application_id);
CREATE INDEX idx_payment_schedules_due_date ON payment_schedules(due_date);
CREATE INDEX idx_payment_schedules_status ON payment_schedules(status);

-- Create triggers for updated_at columns
CREATE TRIGGER update_loan_payments_updated_at 
  BEFORE UPDATE ON loan_payments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_transactions_updated_at 
  BEFORE UPDATE ON payment_transactions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_schedules_updated_at 
  BEFORE UPDATE ON payment_schedules 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE loan_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_schedules ENABLE ROW LEVEL SECURITY;

-- RLS Policies for loan_payments
-- Borrowers can view their own payments
CREATE POLICY "Borrowers can view own payments" ON loan_payments 
  FOR SELECT USING (borrower_id = auth.uid());

-- Borrowers can create their own payments
CREATE POLICY "Borrowers can create payments" ON loan_payments 
  FOR INSERT WITH CHECK (borrower_id = auth.uid());

-- Borrowers can update their own pending payments
CREATE POLICY "Borrowers can update own pending payments" ON loan_payments 
  FOR UPDATE USING (borrower_id = auth.uid() AND status = 'pending');

-- Lenders can view payments for their loans
CREATE POLICY "Lenders can view loan payments" ON loan_payments 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM loan_applications la
      JOIN loan_offers lo ON la.loan_offer_id = lo.id
      WHERE la.id = loan_payments.loan_application_id
      AND lo.lender_id = auth.uid()
    )
  );

-- RLS Policies for payment_transactions
-- Borrowers can view their own transaction records
CREATE POLICY "Borrowers can view own transactions" ON payment_transactions 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM loan_payments lp
      WHERE lp.id = payment_transactions.loan_payment_id
      AND lp.borrower_id = auth.uid()
    )
  );

-- System can insert transaction records
CREATE POLICY "System can insert transactions" ON payment_transactions 
  FOR INSERT WITH CHECK (true);

-- System can update transaction records
CREATE POLICY "System can update transactions" ON payment_transactions 
  FOR UPDATE USING (true);

-- RLS Policies for payment_schedules
-- Borrowers can view their own payment schedules
CREATE POLICY "Borrowers can view own payment schedules" ON payment_schedules 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM loan_applications la
      WHERE la.id = payment_schedules.loan_application_id
      AND la.applicant_id = auth.uid()
    )
  );

-- Lenders can view payment schedules for their loans
CREATE POLICY "Lenders can view loan payment schedules" ON payment_schedules 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM loan_applications la
      JOIN loan_offers lo ON la.loan_offer_id = lo.id
      WHERE la.id = payment_schedules.loan_application_id
      AND lo.lender_id = auth.uid()
    )
  );

-- System can manage payment schedules
CREATE POLICY "System can manage payment schedules" ON payment_schedules 
  FOR ALL USING (true);

-- Functions for payment management

-- Function to generate payment schedule when loan is approved
CREATE OR REPLACE FUNCTION generate_payment_schedule(
  p_loan_application_id UUID,
  p_principal DECIMAL(15,2),
  p_interest_rate DECIMAL(5,2),
  p_duration_months INTEGER,
  p_processing_fee_rate DECIMAL(5,2),
  p_start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) RETURNS VOID AS $$
DECLARE
  monthly_rate DECIMAL(10,6);
  monthly_payment DECIMAL(15,2);
  remaining_balance DECIMAL(15,2);
  due_date_calc TIMESTAMP WITH TIME ZONE;
  month_num INTEGER;
  principal_payment DECIMAL(15,2);
  interest_payment DECIMAL(15,2);
  processing_fee DECIMAL(15,2);
BEGIN
  -- Calculate monthly rate and payment
  monthly_rate := p_interest_rate / 100 / 12;
  monthly_payment := p_principal * (monthly_rate * power(1 + monthly_rate, p_duration_months)) / 
                     (power(1 + monthly_rate, p_duration_months) - 1);
  
  -- Calculate processing fee (only for first payment)
  processing_fee := p_principal * (p_processing_fee_rate / 100);
  
  remaining_balance := p_principal;
  due_date_calc := p_start_date;
  
  -- Generate schedule for each month
  FOR month_num IN 1..p_duration_months LOOP
    due_date_calc := due_date_calc + INTERVAL '1 month';
    
    -- Calculate payment breakdown
    interest_payment := remaining_balance * monthly_rate;
    principal_payment := monthly_payment - interest_payment;
    remaining_balance := remaining_balance - principal_payment;
    
    -- Handle final payment rounding
    IF month_num = p_duration_months AND remaining_balance < 0.01 THEN
      principal_payment := principal_payment + remaining_balance;
      remaining_balance := 0;
    END IF;
    
    -- Insert payment schedule record
    INSERT INTO payment_schedules (
      loan_application_id,
      month_number,
      due_date,
      expected_payment_amount,
      expected_principal,
      expected_interest,
      expected_processing_fee,
      expected_balance
    ) VALUES (
      p_loan_application_id,
      month_num,
      due_date_calc,
      monthly_payment + CASE WHEN month_num = 1 THEN processing_fee ELSE 0 END,
      principal_payment,
      interest_payment,
      CASE WHEN month_num = 1 THEN processing_fee ELSE 0 END,
      remaining_balance
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update payment schedule when payment is made
CREATE OR REPLACE FUNCTION update_payment_schedule_on_payment()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update if payment is completed
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    UPDATE payment_schedules 
    SET 
      actual_payment_amount = actual_payment_amount + NEW.payment_amount,
      actual_principal = actual_principal + NEW.principal_amount,
      actual_interest = actual_interest + NEW.interest_amount,
      actual_processing_fee = actual_processing_fee + NEW.processing_fee,
      status = CASE 
        WHEN (actual_payment_amount + NEW.payment_amount) >= expected_payment_amount THEN 'paid'
        ELSE 'partially_paid'
      END,
      paid_date = CASE 
        WHEN (actual_payment_amount + NEW.payment_amount) >= expected_payment_amount THEN NEW.payment_date
        ELSE paid_date
      END
    WHERE loan_application_id = NEW.loan_application_id
    AND month_number = NEW.scheduled_payment_month;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update payment schedule on payment completion
CREATE TRIGGER update_schedule_on_payment 
  AFTER UPDATE ON loan_payments 
  FOR EACH ROW EXECUTE FUNCTION update_payment_schedule_on_payment();

-- Function to get payment summary for a loan
CREATE OR REPLACE FUNCTION get_loan_payment_summary(p_loan_application_id UUID)
RETURNS TABLE (
  total_expected DECIMAL(15,2),
  total_paid DECIMAL(15,2),
  total_outstanding DECIMAL(15,2),
  next_payment_due TIMESTAMP WITH TIME ZONE,
  next_payment_amount DECIMAL(15,2),
  overdue_amount DECIMAL(15,2),
  payments_remaining INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    SUM(ps.expected_payment_amount) as total_expected,
    SUM(ps.actual_payment_amount) as total_paid,
    SUM(ps.expected_payment_amount - ps.actual_payment_amount) as total_outstanding,
    MIN(CASE WHEN ps.status = 'pending' THEN ps.due_date END) as next_payment_due,
    MIN(CASE WHEN ps.status = 'pending' THEN ps.expected_payment_amount END) as next_payment_amount,
    SUM(CASE WHEN ps.status = 'overdue' THEN ps.expected_payment_amount - ps.actual_payment_amount ELSE 0 END) as overdue_amount,
    COUNT(CASE WHEN ps.status = 'pending' THEN 1 END)::INTEGER as payments_remaining
  FROM payment_schedules ps
  WHERE ps.loan_application_id = p_loan_application_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON loan_payments TO authenticated;
GRANT ALL ON payment_transactions TO authenticated;
GRANT ALL ON payment_schedules TO authenticated;
GRANT EXECUTE ON FUNCTION generate_payment_schedule(UUID, DECIMAL, DECIMAL, INTEGER, DECIMAL, TIMESTAMP WITH TIME ZONE) TO authenticated;
GRANT EXECUTE ON FUNCTION get_loan_payment_summary(UUID) TO authenticated; 