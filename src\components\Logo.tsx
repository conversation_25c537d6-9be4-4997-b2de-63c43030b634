import React from "react";
import Link from "next/link";
import Image from "next/image";

interface LogoProps {
  width?: number;
  height?: number;
  className?: string;
}

const Logo = ({ width = 120, height = 40, className = "" }: LogoProps) => {
  return (
    <Link href="/" passHref>
      <Image
        src="/kredxa-logo.svg"
        alt="Logo"
        width={width}
        height={height}
        className={`cursor-pointer transition-opacity duration-300 hover:opacity-70 ${className}`}
        priority
      />
    </Link>
  );
};

export default Logo;
