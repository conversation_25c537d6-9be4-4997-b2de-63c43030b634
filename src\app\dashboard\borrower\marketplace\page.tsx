"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { getActiveMarketplaceOffers } from "@/lib/loan-offers";
import { checkUserApplication } from "@/lib/loan-applications";
import { Database } from "@/lib/supabase";
import { toast } from "sonner";
import { Loader2, CheckCircle } from "lucide-react";
import LoanApplicationModal from "@/components/marketplace/LoanApplicationModal";
import LoanOfferDetailsModal from "@/components/marketplace/LoanOfferDetailsModal";

type LoanOffer = Database['public']['Tables']['loan_offers']['Row'];

interface EnhancedLoanOffer extends LoanOffer {
  lender_name?: string;
  lender_type?: string;
}

interface MarketplaceLender {
  id: string;
  name: string;
  type: "Individual Lender" | "Corporate Lender";
  rating: number;
  minAmount: string;
  maxAmount: string;
  interestRate: string;
  specialties: string[];
  icon: string;
  about: string;
  productName: string;
  processingFee: number;
  collateralRequired: boolean;
  minDuration: number;
  maxDuration: number;
  rateType: string;
  targetBorrowers: string[];
}

export default function MarketplacePage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAmount, setSelectedAmount] = useState("");
  const [offers, setOffers] = useState<EnhancedLoanOffer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [applicationModal, setApplicationModal] = useState<{
    isOpen: boolean;
    loanOffer: MarketplaceLender | null;
  }>({
    isOpen: false,
    loanOffer: null
  });
  const [detailsModal, setDetailsModal] = useState<{
    isOpen: boolean;
    loanOffer: MarketplaceLender | null;
  }>({
    isOpen: false,
    loanOffer: null
  });
  const [userApplications, setUserApplications] = useState<Map<string, { id: string; status: string; created_at: string }>>(new Map());

  const fetchOffers = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await getActiveMarketplaceOffers();
      if (result.success) {
        setOffers(result.data || []);
        // Check application status for each offer
        await checkApplicationsStatus(result.data || []);
      } else {
        toast.error(result.error || 'Failed to fetch loan offers');
      }
    } catch (error) {
      console.error('Error fetching offers:', error);
      toast.error('An error occurred while fetching loan offers');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check if user has applied for any of the offers
  const checkApplicationsStatus = async (offersList: EnhancedLoanOffer[]) => {
    const applicationsMap = new Map();
    
    for (const offer of offersList) {
      try {
        const result = await checkUserApplication(offer.id);
        if (result.success && result.data) {
          applicationsMap.set(offer.id, result.data);
        }
      } catch (error) {
        console.error(`Error checking application for offer ${offer.id}:`, error);
      }
    }
    
    setUserApplications(applicationsMap);
  };

  // Fetch offers on component mount
  useEffect(() => {
    fetchOffers();
  }, [fetchOffers]);

  const handleApplyNow = (lender: MarketplaceLender) => {
    setApplicationModal({
      isOpen: true,
      loanOffer: lender
    });
  };

  const handleViewDetails = (lender: MarketplaceLender) => {
    setDetailsModal({
      isOpen: true,
      loanOffer: lender
    });
  };

  const handleCloseModal = () => {
    setApplicationModal({
      isOpen: false,
      loanOffer: null
    });
  };

  const handleCloseDetailsModal = () => {
    setDetailsModal({
      isOpen: false,
      loanOffer: null
    });
  };

  const handleApplicationSuccess = () => {
    // Refresh offers and application status
    fetchOffers();
    handleCloseModal();
  };

  // Transform Supabase offers to match the expected lender format
  const transformOfferToLender = (offer: EnhancedLoanOffer): MarketplaceLender => {
    return {
      id: offer.id,
      name: offer.product_name,
      type: (offer.lender_type as "Individual Lender" | "Corporate Lender") || "Individual Lender",
      rating: 4.5, // Default rating - would come from reviews table in production
      minAmount: `₦${offer.min_amount.toLocaleString()}`,
      maxAmount: `₦${offer.max_amount.toLocaleString()}`,
      interestRate: `${offer.interest_rate} ${offer.rate_type}`,
      specialties: offer.target_borrowers || [],
      icon: offer.lender_type === 'Corporate Lender' ? "🏦" : "👨‍💼",
      about: offer.description || "No description available",
      productName: offer.product_name,
      processingFee: offer.processing_fee,
      collateralRequired: offer.collateral_required,
      minDuration: offer.min_duration,
      maxDuration: offer.max_duration,
      rateType: offer.rate_type,
      targetBorrowers: offer.target_borrowers || []
    };
  };

  // Filter offers based on search and amount
  const filteredLenders = offers
    .map(transformOfferToLender)
    .filter(lender => {
      const matchesSearch = !searchTerm || 
        lender.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lender.about.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lender.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase())) ||
        lender.targetBorrowers.some(b => b.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesAmount = !selectedAmount || 
        (parseInt(selectedAmount.replace(/[^0-9]/g, '')) >= parseInt(lender.minAmount.replace(/[^0-9]/g, '')) &&
         parseInt(selectedAmount.replace(/[^0-9]/g, '')) <= parseInt(lender.maxAmount.replace(/[^0-9]/g, '')));
      
      return matchesSearch && matchesAmount;
    });

  const amountOptions = [
    "₦50,000",
    "₦100,000",
    "₦200,000",
    "₦500,000",
    "₦1,000,000",
    "₦2,000,000",
    "₦5,000,000",
    "₦10,000,000",
  ];

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Loan Marketplace</h1>
        <p className="text-gray-500 text-sm mt-1">
          Find and apply for loan offers that match your needs
        </p>
      </div>

      {/* Header Info */}
      <div className="mb-6">
        <p className="text-sm text-gray-600">
          {isLoading ? 'Loading offers...' : `${filteredLenders.length} loan offers available`}
        </p>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Input
          placeholder="Search by loan name, description, or target borrowers..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="md:col-span-2"
        />
        <select
          value={selectedAmount}
          onChange={(e) => setSelectedAmount(e.target.value)}
          className="rounded-md border border-gray-300 px-3 py-2 text-sm"
        >
          <option value="">All Amounts</option>
          {amountOptions.map(amount => (
            <option key={amount} value={amount}>{amount}</option>
          ))}
        </select>
      </div>

      {/* Lenders Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          <div className="col-span-full flex justify-center items-center py-12">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading loan offers...</p>
            </div>
          </div>
        ) : filteredLenders.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-600 mb-4">No loan offers found matching your criteria.</p>
            <Button onClick={fetchOffers} variant="outline">
              Refresh Offers
            </Button>
          </div>
        ) : (
          filteredLenders.map((lender) => (
                      <Card key={lender.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{lender.icon}</span>
                    <div>
                      <CardTitle className="text-lg">{lender.name}</CardTitle>
                      <p className="text-sm text-gray-500">{lender.type}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    {/*
                    <div className="flex items-center gap-1">
                      <span className="text-yellow-500">★</span>
                      <span className="font-medium">{lender.rating}</span>
                    </div>
                    */}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600 line-clamp-2">
                    {lender.about}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Loan Range</p>
                      <p className="font-semibold">{lender.minAmount} - {lender.maxAmount}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Interest Rate</p>
                      <p className="font-semibold">{lender.interestRate}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Duration</p>
                      <p className="font-semibold">{lender.minDuration}-{lender.maxDuration} months</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Processing Fee</p>
                      <p className="font-semibold">{lender.processingFee}%</p>
                    </div>
                  </div>

                  {lender.targetBorrowers.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {lender.targetBorrowers.slice(0, 2).map((borrower, index) => (
                        <span 
                          key={index}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                        >
                          {borrower}
                        </span>
                      ))}
                      {lender.targetBorrowers.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          +{lender.targetBorrowers.length - 2} more
                        </span>
                      )}
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      className="flex-1"
                      onClick={() => handleViewDetails(lender)}
                    >
                      View Details
                    </Button>
                    {userApplications.has(lender.id) ? (
                      <Button
                        className="flex-1"
                        variant="secondary"
                        disabled
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Applied ({userApplications.get(lender.id)?.status})
                      </Button>
                    ) : (
                      <Button
                        className="flex-1"
                        onClick={() => handleApplyNow(lender)}
                      >
                        Apply Now
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Loan Application Modal */}
      {applicationModal.isOpen && applicationModal.loanOffer && (
        <LoanApplicationModal
          isOpen={applicationModal.isOpen}
          onClose={handleCloseModal}
          loanOffer={{
            id: applicationModal.loanOffer.id,
            product_name: applicationModal.loanOffer.name,
            min_amount: parseInt(applicationModal.loanOffer.minAmount.replace(/[^0-9]/g, '')),
            max_amount: parseInt(applicationModal.loanOffer.maxAmount.replace(/[^0-9]/g, '')),
            min_duration: applicationModal.loanOffer.minDuration,
            max_duration: applicationModal.loanOffer.maxDuration,
            interest_rate: parseFloat(applicationModal.loanOffer.interestRate.split('%')[0]),
            rate_type: applicationModal.loanOffer.rateType,
            processing_fee: applicationModal.loanOffer.processingFee,
            collateral_required: applicationModal.loanOffer.collateralRequired,
            lender_name: applicationModal.loanOffer.name,
            lender_type: applicationModal.loanOffer.type
          }}
          onSuccess={handleApplicationSuccess}
        />
      )}

      {/* Loan Offer Details Modal */}
      {detailsModal.isOpen && detailsModal.loanOffer && (
        <LoanOfferDetailsModal
          isOpen={detailsModal.isOpen}
          onClose={handleCloseDetailsModal}
          loanOffer={detailsModal.loanOffer}
          onApply={handleApplyNow}
          hasApplied={userApplications.has(detailsModal.loanOffer.id)}
          applicationStatus={userApplications.get(detailsModal.loanOffer.id)?.status}
        />
      )}
    </div>
  );
}