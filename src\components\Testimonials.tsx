"use client";
import React from 'react';
import Image from 'next/image';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';
const Testimonials = () => {
  const router = useRouter();

  const handleRedirect = () => {
    router.push('/404');
  };

  return (
    <div className="bg-white py-12 sm:py-16"
    style={{
        backgroundImage: "url('/image 18.svg')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <div className="max-w-4xl mx-auto flex flex-col md:flex-row items-center text-center md:text-left px-4">
        <div className="w-full md:w-1/2">
          <h3 className="text-base sm:text-lg font-semibold text-gray-600">Don&apos;t Take Our Word For It</h3>
          <h2 className="text-3xl sm:text-4xl font-bold mt-2">Take Our Customers</h2>
          <p className="text-gray-500 mt-4 text-sm sm:text-base">
            Over 7,000 Happy Customers have many happy investors invest with us. Some impressions from our Customers are down below! Pleased read some of the lovely things our Customers say about us.
          </p>
          <Button
            className="mt-6 bg-black text-white rounded-full px-8 py-3"
            onClick={handleRedirect}
          >
            WHAT WE OFFER
          </Button>
        </div>
        <div className="w-full md:w-1/2 mt-8 md:mt-0">
          <Image src="/image 16.svg" alt="Testimonials" width={400} height={400} className="mx-auto" />
        </div>
      </div>
    </div>
  );
};

export default Testimonials;