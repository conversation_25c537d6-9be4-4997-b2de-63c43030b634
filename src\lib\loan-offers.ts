import { supabase } from './supabase'
import { Database } from './supabase'

// type LoanOffer = Database['public']['Tables']['loan_offers']['Row']
type LoanOfferInsert = Database['public']['Tables']['loan_offers']['Insert']
type LoanOfferUpdate = Database['public']['Tables']['loan_offers']['Update']

export interface CreateLoanOfferData {
  productName: string
  minAmount: string
  maxAmount: string
  minDuration: string
  maxDuration: string
  interestRate: string
  rateType: '% Monthly' | '% Annually'
  description: string
  targetBorrowers: string[]
  collateralRequired: boolean
  processingFee: string
}

// Helper function to convert string amounts to numbers
const parseAmount = (amount: string): number => {
  return parseFloat(amount.replace(/,/g, ''))
}

export async function createLoanOffer(data: CreateLoanOfferData) {
  try {
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('User not authenticated')
    }

    // Prepare data for insertion
    const loanOfferData: LoanOfferInsert = {
      lender_id: user.id,
      product_name: data.productName,
      min_amount: parseAmount(data.minAmount),
      max_amount: parseAmount(data.maxAmount),
      min_duration: parseInt(data.minDuration),
      max_duration: parseInt(data.maxDuration),
      interest_rate: parseFloat(data.interestRate),
      rate_type: data.rateType,
      processing_fee: parseFloat(data.processingFee) || 0,
      collateral_required: data.collateralRequired,
      description: data.description || null,
      target_borrowers: data.targetBorrowers.length > 0 ? data.targetBorrowers : null,
      status: 'active',
      is_active: true
    }

    // Insert loan offer
    const { data: result, error } = await supabase
      .from('loan_offers')
      .insert(loanOfferData)
      .select()
      .single()

    if (error) {
      console.error('Error creating loan offer:', error)
      throw new Error(`Failed to create loan offer: ${error.message}`)
    }

    return { success: true, data: result }
  } catch (error) {
    console.error('Error in createLoanOffer:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    }
  }
}

export async function getLenderOffers(lenderId?: string) {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('User not authenticated')
    }

    const userId = lenderId || user.id

    const { data, error } = await supabase
      .from('loan_offers')
      .select('*')
      .eq('lender_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching loan offers:', error)
      throw new Error(`Failed to fetch loan offers: ${error.message}`)
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in getLenderOffers:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      data: [] 
    }
  }
}

export async function updateLoanOffer(id: string, updates: Partial<LoanOfferUpdate>) {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('loan_offers')
      .update(updates)
      .eq('id', id)
      .eq('lender_id', user.id) // Ensure user can only update their own offers
      .select()
      .single()

    if (error) {
      console.error('Error updating loan offer:', error)
      throw new Error(`Failed to update loan offer: ${error.message}`)
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in updateLoanOffer:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    }
  }
}

export async function deleteLoanOffer(id: string) {
  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      throw new Error('User not authenticated')
    }

    const { error } = await supabase
      .from('loan_offers')
      .delete()
      .eq('id', id)
      .eq('lender_id', user.id) // Ensure user can only delete their own offers

    if (error) {
      console.error('Error deleting loan offer:', error)
      throw new Error(`Failed to delete loan offer: ${error.message}`)
    }

    return { success: true }
  } catch (error) {
    console.error('Error in deleteLoanOffer:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    }
  }
}

export async function getActiveMarketplaceOffers() {
  try {
    // First, get the loan offers
    const { data: offers, error: offersError } = await supabase
      .from('loan_offers')
      .select('*')
      .eq('is_active', true)
      .eq('status', 'active')
      .order('created_at', { ascending: false })

    if (offersError) {
      console.error('Error fetching loan offers:', offersError)
      throw new Error(`Failed to fetch loan offers: ${offersError.message}`)
    }

    if (!offers || offers.length === 0) {
      return { success: true, data: [] }
    }

    // Get unique lender IDs
    const lenderIds = [...new Set(offers.map(offer => offer.lender_id))]

    // Fetch profiles for these lenders
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, user_type')
      .in('id', lenderIds)

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError)
    }

    // Fetch individual accounts for individual lenders
    const individualLenderIds = profiles?.filter(p => p.user_type === 'individual').map(p => p.id) || []
    let individualAccounts: Array<{ user_id: string; full_name: string | null }> = []
    if (individualLenderIds.length > 0) {
      const { data: indAccounts, error: indError } = await supabase
        .from('individual_accounts')
        .select('user_id, full_name')
        .in('user_id', individualLenderIds)
      
      if (!indError) {
        individualAccounts = indAccounts || []
      }
    }

    // Fetch corporate accounts for corporate lenders
    const corporateLenderIds = profiles?.filter(p => p.user_type === 'corporate').map(p => p.id) || []
    let corporateAccounts: Array<{ user_id: string; organization_name: string | null }> = []
    if (corporateLenderIds.length > 0) {
      const { data: corpAccounts, error: corpError } = await supabase
        .from('corporate_accounts')
        .select('user_id, organization_name')
        .in('user_id', corporateLenderIds)
      
      if (!corpError) {
        corporateAccounts = corpAccounts || []
      }
    }

    // Create lookup maps
    const profileMap = new Map()
    profiles?.forEach(profile => {
      profileMap.set(profile.id, profile)
    })

    const individualAccountMap = new Map()
    individualAccounts.forEach(account => {
      individualAccountMap.set(account.user_id, account)
    })

    const corporateAccountMap = new Map()
    corporateAccounts.forEach(account => {
      corporateAccountMap.set(account.user_id, account)
    })

    // Transform the data to include lender info
    const transformedData = offers.map(offer => {
      const profile = profileMap.get(offer.lender_id)
      const individualAccount = individualAccountMap.get(offer.lender_id)
      const corporateAccount = corporateAccountMap.get(offer.lender_id)
      
      return {
        ...offer,
        lender_name: individualAccount?.full_name || 
                     corporateAccount?.organization_name || 
                     'Anonymous Lender',
        lender_type: profile?.user_type === 'corporate' ? 'Corporate Lender' : 'Individual Lender'
      }
    })

    return { success: true, data: transformedData }
  } catch (error) {
    console.error('Error in getActiveMarketplaceOffers:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      data: [] 
    }
  }
} 