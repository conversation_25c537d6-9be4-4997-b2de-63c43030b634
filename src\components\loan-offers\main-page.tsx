import React from 'react';
import DefineLoanSection from './define-loan-section';
import ReviewAndConfirm from './review&confirm-section';
import Header from './header';


const MainPage = () => {
    return (
        <>
        <div className='bg-[#F6F7F8] min-h-screen'>
            <Header />
            <div className='max-w-2xl mx-auto p-4 pt-8'>
                <h1 className='text-2xl font-bold text-[#1a1a1a] mb-2'>Create a New Loan Offer</h1>
                <p className='text-sm text-[#1a1a1a] mb-6'>Set the loan terms that borrowers will see on the marketplace. Eligibility and repayment settings are managed by Kredxa to ensure fairness and consistency.</p>
            </div>
            <DefineLoanSection />
            <ReviewAndConfirm />
        </div>
        </>
    );
}

export default MainPage;
