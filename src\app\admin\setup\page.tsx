"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { getCurrentUser, setAdminStatus } from "@/lib/auth-supabase";
import { supabase } from "@/lib/supabase";
import { User } from "@/lib/types";

export default function AdminSetupPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSettingUp, setIsSettingUp] = useState(false);
  const [setupEmail, setSetupEmail] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    checkUserAndSetup();
  }, []);

  const checkUserAndSetup = async () => {
    try {
      const user = await getCurrentUser();
      if (!user) {
        setError("Please log in first to set up admin access.");
        setIsLoading(false);
        return;
      }

      setCurrentUser(user);
      setSetupEmail(user.email || "");

      // Check if user is already admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('is_admin, admin_level')
        .eq('id', user.id)
        .single();

      if (profile?.is_admin) {
        setError("You are already an admin user.");
        setIsLoading(false);
        return;
      }

      // Check if there are any existing admins
      const { data: adminUsers } = await supabase
        .from('admin_users')
        .select('id')
        .eq('is_active', true)
        .limit(1);

      if (adminUsers && adminUsers.length > 0) {
        setError("Admin users already exist. Contact an existing admin for access.");
        setIsLoading(false);
        return;
      }

      setIsLoading(false);
    } catch (error: unknown) {
      console.error("Setup check failed:", error);
      setError("Failed to check setup status. Please try again.");
      setIsLoading(false);
    }
  };

  const setupFirstAdmin = async () => {
    if (!setupEmail.trim()) {
      setError("Please enter an email address");
      return;
    }

    if (!currentUser?.id) {
      setError("No user found. Please log in again.");
      return;
    }

    setIsSettingUp(true);
    setError("");
    
    try {
      // Set the current user as super admin
      const result = await setAdminStatus(currentUser.id, true, 'super_admin');
      
      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          window.location.href = '/admin';
        }, 2000);
      } else {
        setError(`Failed to setup admin: ${result.error}`);
      }
    } catch (error: unknown) {
      console.error("Admin setup failed:", error);
      setError("Failed to setup admin privileges. Please try again.");
    } finally {
      setIsSettingUp(false);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">Checking setup status...</p>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">Admin Setup Successful!</h3>
          <p className="mt-2 text-sm text-gray-600">
            You now have super admin privileges. Redirecting to admin panel...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
        <Card>
          <CardHeader>
            <CardTitle>Admin Setup</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm text-gray-600 mb-4">
                  Set up the first admin user to access the admin panel.
                </p>
                <p className="text-sm text-gray-500 mb-4">
                  Logged in as: {currentUser?.email}
                </p>
              </div>
              
              <div>
                <label htmlFor="setupEmail" className="block text-sm font-medium text-gray-700 mb-1">
                  Admin Email
                </label>
                <Input
                  type="email"
                  id="setupEmail"
                  placeholder="Enter admin email"
                  value={setupEmail}
                  onChange={(e) => setSetupEmail(e.target.value)}
                  className="w-full"
                />
              </div>
              
              <Button 
                onClick={setupFirstAdmin} 
                disabled={isSettingUp || !setupEmail.trim()}
                className="w-full"
              >
                {isSettingUp ? "Setting up..." : "Setup Super Admin"}
              </Button>
              
              <div className="text-xs text-gray-500">
                <p>This will create a super admin account with full privileges.</p>
                <p>You can create additional admin users later from the admin panel.</p>
              </div>
              
              <div className="pt-4 border-t">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Prerequisites:</h4>
                <ol className="text-xs text-gray-500 list-decimal list-inside space-y-1">
                  <li>You must be logged in with a regular account</li>
                  <li>Database migration <code className="bg-gray-100 px-1 rounded">005_add_admin_roles.sql</code> must be run</li>
                  <li>No existing admin users in the system</li>
                </ol>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 