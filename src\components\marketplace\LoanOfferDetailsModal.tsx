import React from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Clock, 
  DollarSign, 
  Percent, 
  FileText, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  Star,
  Building,
  User
} from 'lucide-react';

interface MarketplaceLender {
  id: string;
  name: string;
  type: "Individual Lender" | "Corporate Lender";
  rating: number;
  minAmount: string;
  maxAmount: string;
  interestRate: string;
  specialties: string[];
  icon: string;
  about: string;
  productName: string;
  processingFee: number;
  collateralRequired: boolean;
  minDuration: number;
  maxDuration: number;
  rateType: string;
  targetBorrowers: string[];
}

interface LoanOfferDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  loanOffer: MarketplaceLender;
  onApply: (loanOffer: MarketplaceLender) => void;
  hasApplied?: boolean;
  applicationStatus?: string;
}

const LoanOfferDetailsModal: React.FC<LoanOfferDetailsModalProps> = ({
  isOpen,
  onClose,
  loanOffer,
  onApply,
  hasApplied = false,
  applicationStatus
}) => {
  const formatAmount = (amount: string) => {
    return amount.replace(/₦/g, '').replace(/,/g, '');
  };

  const calculateExamplePayment = () => {
    const principal = parseInt(formatAmount(loanOffer.maxAmount)) / 2; // Example with half of max amount
    const rate = parseFloat(loanOffer.interestRate.split('%')[0]) / 100;
    const duration = (loanOffer.minDuration + loanOffer.maxDuration) / 2; // Average duration
    
    let monthlyPayment;
    if (loanOffer.rateType === 'per_annum') {
      const monthlyRate = rate / 12;
      monthlyPayment = (principal * monthlyRate * Math.pow(1 + monthlyRate, duration)) / 
                      (Math.pow(1 + monthlyRate, duration) - 1);
    } else {
      monthlyPayment = principal * (1 + rate) / duration;
    }
    
    return Math.round(monthlyPayment);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span className="text-xl">{loanOffer.icon}</span>
            <div>
              <h2 className="text-lg font-bold">{loanOffer.name}</h2>
              <p className="text-xs text-gray-500 font-normal">{loanOffer.type}</p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-4">
            {/* Lender Rating */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Star className="w-4 h-4 text-yellow-500" />
                  Lender Rating
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-4 h-4 ${
                          star <= loanOffer.rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="font-semibold">{loanOffer.rating}/5</span>
                  <span className="text-sm text-gray-500">(Based on borrower reviews)</span>
                </div>
              </CardContent>
            </Card>

            {/* About */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <FileText className="w-4 h-4" />
                  About This Loan Product
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed">{loanOffer.about}</p>
              </CardContent>
            </Card>

            {/* Target Borrowers */}
            {loanOffer.targetBorrowers.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-base">
                    {loanOffer.type === "Corporate Lender" ? (
                      <Building className="w-4 h-4" />
                    ) : (
                      <User className="w-4 h-4" />
                    )}
                    Target Borrowers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {loanOffer.targetBorrowers.map((borrower, index) => (
                      <Badge key={index} variant="secondary" className="text-sm">
                        {borrower}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Example Payment Calculation */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <DollarSign className="w-4 h-4" />
                  Example Payment Calculation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div>
                      <p className="text-gray-500">Loan Amount</p>
                      <p className="font-semibold text-sm">₦{(parseInt(formatAmount(loanOffer.maxAmount)) / 2).toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Duration</p>
                      <p className="font-semibold text-sm">{Math.round((loanOffer.minDuration + loanOffer.maxDuration) / 2)} months</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Interest Rate</p>
                      <p className="font-semibold text-sm">{loanOffer.interestRate}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Est. Monthly Payment</p>
                      <p className="font-semibold text-sm text-blue-600">₦{calculateExamplePayment().toLocaleString()}</p>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    *This is an example calculation. Actual payments may vary based on your specific loan terms.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Key Details */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Loan Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 pt-3">
                <div className="flex items-center gap-2">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <div>
                    <p className="text-xs text-gray-500">Loan Range</p>
                    <p className="font-semibold text-sm">{loanOffer.minAmount} - {loanOffer.maxAmount}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Percent className="w-4 h-4 text-blue-600" />
                  <div>
                    <p className="text-xs text-gray-500">Interest Rate</p>
                    <p className="font-semibold text-sm">{loanOffer.interestRate}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-orange-600" />
                  <div>
                    <p className="text-xs text-gray-500">Duration</p>
                    <p className="font-semibold text-sm">{loanOffer.minDuration}-{loanOffer.maxDuration} months</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-purple-600" />
                  <div>
                    <p className="text-xs text-gray-500">Processing Fee</p>
                    <p className="font-semibold text-sm">{loanOffer.processingFee}%</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {loanOffer.collateralRequired ? (
                    <AlertCircle className="w-4 h-4 text-red-600" />
                  ) : (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  )}
                  <div>
                    <p className="text-xs text-gray-500">Collateral</p>
                    <p className="font-semibold text-sm">
                      {loanOffer.collateralRequired ? 'Required' : 'Not Required'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <Shield className="w-4 h-4" />
                  Requirements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Valid identification document</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Proof of income</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Bank account details</span>
                  </li>
                  {loanOffer.collateralRequired && (
                    <li className="flex items-start gap-2">
                      <AlertCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                      <span>Collateral documentation</span>
                    </li>
                  )}
                </ul>
              </CardContent>
            </Card>

            {/* Actions */}
            <div className="space-y-3">
              {hasApplied ? (
                <Button className="w-full" variant="secondary" disabled>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Applied ({applicationStatus})
                </Button>
              ) : (
                <Button 
                  className="w-full" 
                  onClick={() => onApply(loanOffer)}
                  size="lg"
                >
                  Apply for This Loan
                </Button>
              )}
              
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={onClose}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoanOfferDetailsModal; 