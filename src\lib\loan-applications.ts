import { supabase } from './supabase'
import { Database } from './supabase'

type LoanApplicationRow = Database['public']['Tables']['loan_applications']['Row']

export interface LoanApplicationData {
  loan_offer_id: string
  requested_amount: number
  requested_duration: number
  purpose: string
  
  // Personal information
  full_name: string
  email: string
  phone_number?: string
  date_of_birth?: string
  
  // Employment information
  employment_status: 'employed' | 'self-employed' | 'unemployed' | 'student' | 'retired'
  employer_name?: string
  job_title?: string
  monthly_income?: number
  work_duration?: number
  
  // Financial information
  monthly_expenses?: number
  existing_loans?: number
  bank_name?: string
  account_number?: string
  bvn?: string
  
  // Guarantor information (optional)
  guarantor_name?: string
  guarantor_phone?: string
  guarantor_email?: string
  guarantor_relationship?: string
}

export interface LoanApplicationResult {
  success: boolean
  data?: LoanApplicationRow[] | LoanApplicationRow | Partial<LoanApplicationRow> | null
  error?: string
}

// Create a new loan application
export async function createLoanApplication(applicationData: LoanApplicationData): Promise<LoanApplicationResult> {
  try {
    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' }
    }

    // Check if user has already applied for this loan offer
    const { data: existingApplication } = await supabase
      .rpc('check_duplicate_application', {
        p_applicant_id: user.id,
        p_loan_offer_id: applicationData.loan_offer_id
      })

    if (existingApplication) {
      return { success: false, error: 'You have already applied for this loan offer' }
    }

    // Validate the requested amount and duration against the loan offer
    const { data: loanOffer, error: offerError } = await supabase
      .from('loan_offers')
      .select('min_amount, max_amount, min_duration, max_duration, is_active, status')
      .eq('id', applicationData.loan_offer_id)
      .single()

    if (offerError || !loanOffer) {
      return { success: false, error: 'Loan offer not found' }
    }

    if (!loanOffer.is_active || loanOffer.status !== 'active') {
      return { success: false, error: 'This loan offer is no longer available' }
    }

    if (applicationData.requested_amount < loanOffer.min_amount || 
        applicationData.requested_amount > loanOffer.max_amount) {
      return { 
        success: false, 
        error: `Requested amount must be between ₦${loanOffer.min_amount.toLocaleString()} and ₦${loanOffer.max_amount.toLocaleString()}` 
      }
    }

    if (applicationData.requested_duration < loanOffer.min_duration || 
        applicationData.requested_duration > loanOffer.max_duration) {
      return { 
        success: false, 
        error: `Loan duration must be between ${loanOffer.min_duration} and ${loanOffer.max_duration} months` 
      }
    }

    // Create the application
    const { data, error } = await supabase
      .from('loan_applications')
      .insert({
        applicant_id: user.id,
        ...applicationData
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating loan application:', error)
      return { success: false, error: 'Failed to submit loan application' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in createLoanApplication:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Get user's loan applications
export async function getUserLoanApplications(): Promise<LoanApplicationResult> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' }
    }

    const { data, error } = await supabase
      .from('loan_applications')
      .select(`
        *,
        loan_offers(
          product_name,
          interest_rate,
          rate_type,
          processing_fee,
          profiles(
            individual_accounts(full_name),
            corporate_accounts(organization_name)
          )
        )
      `)
      .eq('applicant_id', user.id)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching loan applications:', error)
      return { success: false, error: 'Failed to fetch loan applications' }
    }

    // Transform data to include lender info
    const transformedData = data?.map(app => ({
      ...app,
      lender_name: app.loan_offers?.profiles?.individual_accounts?.[0]?.full_name ||
                   app.loan_offers?.profiles?.corporate_accounts?.[0]?.organization_name ||
                   'Unknown Lender'
    }))

    return { success: true, data: transformedData }
  } catch (error) {
    console.error('Error in getUserLoanApplications:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Get applications for a specific loan offer (for lenders)
export async function getLoanOfferApplications(loanOfferId: string): Promise<LoanApplicationResult> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' }
    }

    const { data, error } = await supabase
      .from('loan_applications')
      .select(`
        *,
        profiles(
          individual_accounts(full_name),
          corporate_accounts(organization_name)
        )
      `)
      .eq('loan_offer_id', loanOfferId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching loan offer applications:', error)
      return { success: false, error: 'Failed to fetch applications' }
    }

    // Transform data to include applicant info
    const transformedData = data?.map(app => ({
      ...app,
      applicant_name: app.profiles?.individual_accounts?.[0]?.full_name ||
                      app.profiles?.corporate_accounts?.[0]?.organization_name ||
                      'Unknown Applicant'
    }))

    return { success: true, data: transformedData }
  } catch (error) {
    console.error('Error in getLoanOfferApplications:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Update application status (for lenders)
export async function updateApplicationStatus(
  applicationId: string, 
  status: 'under_review' | 'approved' | 'rejected',
  rejectionReason?: string
): Promise<LoanApplicationResult> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' }
    }

    const updateData: {
      status: string
      review_date: string
      approval_date?: string
      rejection_reason?: string
    } = {
      status,
      review_date: new Date().toISOString()
    }

    if (status === 'approved') {
      updateData.approval_date = new Date().toISOString()
    } else if (status === 'rejected' && rejectionReason) {
      updateData.rejection_reason = rejectionReason
    }

    const { data, error } = await supabase
      .from('loan_applications')
      .update(updateData)
      .eq('id', applicationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating application status:', error)
      return { success: false, error: 'Failed to update application status' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in updateApplicationStatus:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Withdraw application (for borrowers)
export async function withdrawApplication(applicationId: string): Promise<LoanApplicationResult> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' }
    }

    const { data, error } = await supabase
      .from('loan_applications')
      .update({ 
        status: 'withdrawn',
        review_date: new Date().toISOString()
      })
      .eq('id', applicationId)
      .eq('applicant_id', user.id) // Ensure user can only withdraw their own applications
      .select()
      .single()

    if (error) {
      console.error('Error withdrawing application:', error)
      return { success: false, error: 'Failed to withdraw application' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in withdrawApplication:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Get application statistics for a loan offer
export async function getApplicationStats(loanOfferId: string): Promise<LoanApplicationResult> {
  try {
    const { data, error } = await supabase
      .rpc('get_application_stats', { p_loan_offer_id: loanOfferId })

    if (error) {
      console.error('Error fetching application stats:', error)
      return { success: false, error: 'Failed to fetch application statistics' }
    }

    return { success: true, data: data?.[0] }
  } catch (error) {
    console.error('Error in getApplicationStats:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
}

// Check if user has applied for a loan offer
export async function checkUserApplication(loanOfferId: string): Promise<LoanApplicationResult> {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' }
    }

    const { data, error } = await supabase
      .from('loan_applications')
      .select('id, status, created_at')
      .eq('applicant_id', user.id)
      .eq('loan_offer_id', loanOfferId)
      .neq('status', 'withdrawn')
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
      console.error('Error checking user application:', error)
      return { success: false, error: 'Failed to check application status' }
    }

    return { success: true, data }
  } catch (error) {
    console.error('Error in checkUserApplication:', error)
    return { success: false, error: 'An unexpected error occurred' }
  }
} 