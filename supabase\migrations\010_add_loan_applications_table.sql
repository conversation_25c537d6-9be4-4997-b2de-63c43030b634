-- Create loan_applications table
CREATE TABLE loan_applications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  applicant_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  loan_offer_id UUID REFERENCES loan_offers(id) ON DELETE CASCADE NOT NULL,
  
  -- Application details
  requested_amount DECIMAL(15, 2) NOT NULL,
  requested_duration INTEGER NOT NULL, -- in months
  purpose TEXT NOT NULL,
  
  -- Personal information
  full_name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone_number TEXT,
  date_of_birth DATE,
  
  -- Employment information
  employment_status TEXT CHECK (employment_status IN ('employed', 'self-employed', 'unemployed', 'student', 'retired')) NOT NULL,
  employer_name TEXT,
  job_title TEXT,
  monthly_income DECIMAL(15, 2),
  work_duration INTEGER, -- in months
  
  -- Financial information
  monthly_expenses DECIMAL(15, 2),
  existing_loans DECIMAL(15, 2) DEFAULT 0,
  bank_name TEXT,
  account_number TEXT,
  bvn TEXT,
  
  -- Guarantor information (optional)
  guarantor_name TEXT,
  guarantor_phone TEXT,
  guarantor_email TEXT,
  guarantor_relationship TEXT,
  
  -- Application status and tracking
  status TEXT CHECK (status IN ('pending', 'under_review', 'approved', 'rejected', 'withdrawn')) DEFAULT 'pending',
  application_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  review_date TIMESTAMP WITH TIME ZONE,
  approval_date TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT valid_amount CHECK (requested_amount > 0),
  CONSTRAINT valid_duration CHECK (requested_duration > 0),
  CONSTRAINT valid_income CHECK (monthly_income IS NULL OR monthly_income >= 0),
  CONSTRAINT valid_expenses CHECK (monthly_expenses IS NULL OR monthly_expenses >= 0),
  CONSTRAINT valid_existing_loans CHECK (existing_loans >= 0)
);

-- Create indexes for better performance
CREATE INDEX idx_loan_applications_applicant ON loan_applications(applicant_id);
CREATE INDEX idx_loan_applications_offer ON loan_applications(loan_offer_id);
CREATE INDEX idx_loan_applications_status ON loan_applications(status);
CREATE INDEX idx_loan_applications_date ON loan_applications(application_date);

-- Create trigger for updated_at
CREATE TRIGGER update_loan_applications_updated_at 
  BEFORE UPDATE ON loan_applications 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE loan_applications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Applicants can view their own applications
CREATE POLICY "Users can view own applications" ON loan_applications 
  FOR SELECT USING (auth.uid() = applicant_id);

-- Applicants can create their own applications
CREATE POLICY "Users can create own applications" ON loan_applications 
  FOR INSERT WITH CHECK (auth.uid() = applicant_id);

-- Applicants can update their own pending applications
CREATE POLICY "Users can update own pending applications" ON loan_applications 
  FOR UPDATE USING (
    auth.uid() = applicant_id 
    AND status IN ('pending', 'under_review')
  );

-- Lenders can view applications for their loan offers
CREATE POLICY "Lenders can view applications for their offers" ON loan_applications 
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM loan_offers 
      WHERE loan_offers.id = loan_applications.loan_offer_id 
      AND loan_offers.lender_id = auth.uid()
    )
  );

-- Lenders can update application status for their offers
CREATE POLICY "Lenders can update application status" ON loan_applications 
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM loan_offers 
      WHERE loan_offers.id = loan_applications.loan_offer_id 
      AND loan_offers.lender_id = auth.uid()
    )
  );

-- Create function to check if user has already applied for a loan offer
CREATE OR REPLACE FUNCTION check_duplicate_application(
  p_applicant_id UUID,
  p_loan_offer_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM loan_applications 
    WHERE applicant_id = p_applicant_id 
    AND loan_offer_id = p_loan_offer_id
    AND status NOT IN ('rejected', 'withdrawn')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get application statistics
CREATE OR REPLACE FUNCTION get_application_stats(p_loan_offer_id UUID)
RETURNS TABLE(
  total_applications BIGINT,
  pending_applications BIGINT,
  approved_applications BIGINT,
  rejected_applications BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_applications,
    COUNT(*) FILTER (WHERE status = 'pending') as pending_applications,
    COUNT(*) FILTER (WHERE status = 'approved') as approved_applications,
    COUNT(*) FILTER (WHERE status = 'rejected') as rejected_applications
  FROM loan_applications
  WHERE loan_offer_id = p_loan_offer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 