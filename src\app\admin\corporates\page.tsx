"use client";

import React, { useState, useEffect } from 'react';
import { 
    getAllCorporateAccounts, 
    setCorporateAccountActive,
} from '@/lib/auth-supabase';
import { CorporateAccount } from '@/lib/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Eye, Edit, UserCheck, UserX } from 'lucide-react';
import ViewCorporateAccountModal from '@/components/admin/ViewCorporateAccountModal';
import EditCorporateAccountModal from '@/components/admin/EditCorporateAccountModal';

export default function CorporatesPage() {
  const [accounts, setAccounts] = useState<CorporateAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<CorporateAccount | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    setIsLoading(true);
    const result = await getAllCorporateAccounts();
    if (result.success && result.accounts) {
      setAccounts(result.accounts);
    } else {
      console.error("Failed to load corporate accounts:", result.error);
      alert("Could not load corporate accounts.");
    }
    setIsLoading(false);
  };

  const handleActiveToggle = async (userId: string, isActive: boolean) => {
    setIsUpdating(true);
    const result = await setCorporateAccountActive(userId, isActive);
    if (result.success) {
      alert(`Account ${isActive ? 'enabled' : 'disabled'} successfully`);
      loadAccounts();
    } else {
      alert(`Failed to update account status: ${result.error}`);
    }
    setIsUpdating(false);
  }

  const getStatusBadge = (status: string, isActive: boolean) => {
    const statusColors = {
      pending: "bg-yellow-100 text-yellow-800",
      verified: "bg-green-100 text-green-800",
      approved: "bg-green-100 text-green-800",
      rejected: "bg-red-100 text-red-800",
    };

    if (!isActive) {
        return <Badge className="bg-gray-200 text-gray-800">Disabled</Badge>;
    }

    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };
  
  const filteredAccounts = accounts.filter(account =>
    account.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.organization_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.approval_status.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return <div>Loading accounts...</div>;
  }

  return (
    <>
        <Card>
        <CardHeader>
            <div className="flex justify-between items-center">
                <div>
                    <CardTitle>Corporate Accounts</CardTitle>
                    <p className="text-gray-500 text-sm mt-1">Manage corporate user accounts.</p>
                </div>
                <Input
                    type="text"
                    placeholder="Search accounts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full max-w-sm"
                />
            </div>
        </CardHeader>
        <CardContent>
            <div className="space-y-4">
            {filteredAccounts.map((account) => (
                <div key={account.user_id} className="p-4 border rounded-lg">
                <div className="flex justify-between items-center">
                    <div className="grid gap-1">
                        <h3 className="font-medium">{account.organization_name || 'N/A'}</h3>
                        <p className="text-sm text-gray-600">{account.email}</p>
                        <p className="text-xs text-gray-500">Joined: {new Date(account.created_at).toLocaleDateString()}</p>
                    </div>
                    <div className="flex items-center space-x-4">
                        {getStatusBadge(account.approval_status, account.is_active)}
                        <div className="flex items-center space-x-2">
                             <Button variant="ghost" size="icon" onClick={() => { setSelectedAccount(account); setIsViewModalOpen(true); }}>
                                <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" onClick={() => { setSelectedAccount(account); setIsEditModalOpen(true); }}>
                                <Edit className="h-4 w-4" />
                            </Button>
                            {account.is_active ? (
                                <Button variant="ghost" size="icon" disabled={isUpdating} onClick={() => handleActiveToggle(account.user_id, false)}>
                                    <UserX className="h-4 w-4 text-red-500" />
                                </Button>
                            ) : (
                                <Button variant="ghost" size="icon" disabled={isUpdating} onClick={() => handleActiveToggle(account.user_id, true)}>
                                    <UserCheck className="h-4 w-4 text-green-500" />
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
                </div>
            ))}
            </div>
        </CardContent>
        </Card>

        <ViewCorporateAccountModal
            account={selectedAccount}
            isOpen={isViewModalOpen}
            onClose={() => setIsViewModalOpen(false)}
        />
        <EditCorporateAccountModal
            account={selectedAccount}
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            onSave={() => {
                setIsEditModalOpen(false);
                loadAccounts();
            }}
        />
    </>
  );
} 