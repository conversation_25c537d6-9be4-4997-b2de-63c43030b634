"use client"

import { useState } from "react"
import { Star } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import LoanDetailsPopup from "../loan-popup/loan-popup"
import { loanOffers, LoanOffer } from "@/data/mockLenders"

export default function CorporateLoanOffers() {
  const [selectedOffer, setSelectedOffer] = useState<LoanOffer | null>(null)
  const [compareCount, setCompareCount] = useState(0)
  const [checkedOffers, setCheckedOffers] = useState<Set<string>>(new Set())

  // Filter for corporate lenders (business loans)
  const corporateLoanOffers = loanOffers.corporate

  const handleCompareChange = (offerId: string, checked: boolean) => {
    const newCheckedOffers = new Set(checkedOffers)
    if (checked) {
      newCheckedOffers.add(offerId)
    } else {
      newCheckedOffers.delete(offerId)
    }
    setCheckedOffers(newCheckedOffers)
    setCompareCount(newCheckedOffers.size)
  }

  const handleViewDetails = (offer: LoanOffer) => {
    setSelectedOffer(offer)
  }

  const handleClosePopup = () => {
    setSelectedOffer(null)
  }

  const handleCompareSelected = () => {
    // Implement comparison functionality here
    console.log("Comparing offers:", Array.from(checkedOffers))
    // You would typically navigate to a comparison page or open a comparison modal
  }

  return (
    <>
    <div style={{paddingLeft: '64px',paddingRight: '64px'}}>
      <div className="py-12 px-4 md:px-8 max-w-[1400px] mx-auto">
        <div className="mb-8 flex justify-between items-center flex-wrap gap-4">
          <h2 className="text-2xl md:text-3xl font-bold text-[#1a1a1a]">Corporate Loan Offers</h2>
          <Button 
            onClick={handleCompareSelected}
            className="bg-[#2D0A0A] text-white px-4 py-2 rounded-md flex items-center gap-2"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5">
              <path d="M9 6H20M9 12H20M9 18H20M5 6L3 8L5 10M5 12L3 14L5 16" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span className="font-medium">Compare Selected</span>
            <span className="bg-white text-black w-10 h-10 flex items-center justify-center rounded-md ml-2 font-bold">
              {compareCount}
            </span>
          </Button>
        </div>

        {/* Loan Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {corporateLoanOffers.map((offer) => (
            <div
              key={offer.id}
              className="bg-white rounded-3xl p-6 shadow-md border border-gray-100 flex flex-col h-fit"
            >
              {/* Lender Header */}
              <div className="flex items-center justify-between mb-6 flex-wrap gap-3">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center overflow-hidden">
                    <span className="text-2xl">{offer.lenderIcon}</span>
                  </div>
                  <div className="min-w-0">
                    <span className="text-xl font-bold text-[#1a1a1a] break-words block">
                      {offer.lenderName}
                    </span>
                    <span className="text-sm text-gray-600 block">
                      {offer.productName}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-1.5 flex-shrink-0">
                  <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium text-[#1a1a1a]">{offer.rating}</span>
                </div>
              </div>

              {/* Loan Details */}
              <div className="flex flex-col gap-4 mb-6">
                {[
                  { label: "Loan Amount", value: `₦${offer.minAmount} - ₦${offer.maxAmount}` },
                  { label: "Interest Rate", value: `${offer.interestRate} ${offer.rateType}` },
                  { label: "Duration", value: `${offer.minDuration}-${offer.maxDuration} months` },
                  { label: "Collateral", value: offer.collateralRequired ? "Required" : "Not Required" },
                  { label: "Processing Fee", value: `${offer.processingFee}%` },
                ].map((item, index) => (
                  <div key={index} className="flex justify-between items-start gap-4">
                    <span className="text-sm text-gray-600 font-medium">{item.label}</span>
                    <span className="text-sm text-[#1a1a1a] font-semibold text-right">{item.value}</span>
                  </div>
                ))}
              </div>

              {/* Target Borrowers */}
              <div className="mb-6">
                <span className="text-sm text-gray-600 font-medium block mb-2">Target Borrowers</span>
                <div className="flex flex-wrap gap-1">
                  {offer.targetBorrowers.slice(0, 2).map((borrower: string, index: number) => (
                    <span key={index} className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      {borrower}
                    </span>
                  ))}
                  {offer.targetBorrowers.length > 2 && (
                    <span className="text-xs text-gray-500">+{offer.targetBorrowers.length - 2} more</span>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between gap-3 mt-auto">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={checkedOffers.has(offer.id)}
                    onCheckedChange={(checked) => handleCompareChange(offer.id, checked as boolean)}
                  />
                  <span className="text-xs text-gray-600">Compare</span>
                </div>
                <Button 
                  onClick={() => handleViewDetails(offer)}
                  className="bg-[#2D0A0A] text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-[#1a0808] transition-colors"
                >
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Loan Details Popup */}
    {selectedOffer && (
      <LoanDetailsPopup
        offer={selectedOffer}
        onClose={handleClosePopup}
      />
    )}
    </>
  )
}