"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { 
  getAllTickets, 
  getTicketWithMessages, 
  addMessageToTicket, 
  updateTicketStatus,
  getTicketStats,
  testSupportSystem,
  TicketWithMessages 
} from "@/lib/support";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";

export default function AdminSupportPage() {
  const [tickets, setTickets] = useState<TicketWithMessages[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<TicketWithMessages | null>(null);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [stats, setStats] = useState({ total: 0, open: 0, in_progress: 0, resolved: 0, closed: 0 });

  useEffect(() => {
    fetchUserData();
  }, []);

  useEffect(() => {
    if (userId) {
      // Run test first
      testSupportSystem();
      fetchTickets();
      fetchStats();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, statusFilter, priorityFilter]);

  const fetchUserData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Admin user:', user);
      if (user) {
        setUserId(user.id);
      } else {
        console.log('No user found');
      }
    } catch (error) {
      console.error('Error fetching user:', error);
    }
  };

  const fetchTickets = async () => {
    setIsLoading(true);
    try {
      console.log('Fetching tickets with filters:', { statusFilter, priorityFilter });
      const allTickets = await getAllTickets(
        statusFilter === 'all' ? undefined : statusFilter,
        priorityFilter === 'all' ? undefined : priorityFilter
      );
      console.log('Fetched tickets:', allTickets);
      setTickets(allTickets);
    } catch (error) {
      console.error('Error fetching tickets:', error);
      toast.error('Failed to fetch support tickets');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const ticketStats = await getTicketStats();
      setStats(ticketStats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleViewTicket = async (ticketId: string) => {
    try {
      const ticketWithMessages = await getTicketWithMessages(ticketId);
      if (ticketWithMessages) {
        setSelectedTicket(ticketWithMessages);
        setIsTicketModalOpen(true);
      }
    } catch (error) {
      console.error('Error fetching ticket details:', error);
      toast.error('Failed to load ticket details');
    }
  };

  const handleSendMessage = async () => {
    if (!selectedTicket || !userId || !newMessage.trim()) return;

    setIsSending(true);
    try {
      const message = await addMessageToTicket(
        selectedTicket.id,
        userId,
        'admin',
        newMessage
      );

      if (message) {
        setSelectedTicket(prev => prev ? {
          ...prev,
          messages: [...prev.messages, message]
        } : null);
        setNewMessage("");
        toast.success('Message sent successfully');
        
        // If ticket was open, move to in_progress
        if (selectedTicket.status === 'open') {
          await handleUpdateStatus(selectedTicket.id, 'in_progress');
        }
      } else {
        toast.error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsSending(false);
    }
  };

  const handleUpdateStatus = async (ticketId: string, newStatus: 'open' | 'in_progress' | 'resolved' | 'closed', assignedTo?: string) => {
    try {
              const success = await updateTicketStatus(ticketId, newStatus, assignedTo);
      if (success) {
        toast.success('Ticket status updated successfully');
        if (selectedTicket && selectedTicket.id === ticketId) {
          setSelectedTicket(prev => prev ? { ...prev, status: newStatus } : null);
        }
        fetchTickets();
        fetchStats();
      } else {
        toast.error('Failed to update ticket status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update ticket status');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const filteredTickets = tickets.filter(ticket =>
    ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ticket.user_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    ticket.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Support Management</h1>
          <p className="text-gray-500 text-sm mt-1">Loading support tickets...</p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Support Management</h1>
        <p className="text-gray-500 text-sm mt-1">
          Manage and respond to support tickets from users
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Tickets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Open</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.open}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">In Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.in_progress}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Resolved</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.resolved}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Closed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.closed}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Search tickets, users, or categories..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tickets List */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Tickets ({filteredTickets.length})</TabsTrigger>
          <TabsTrigger value="urgent">Urgent ({filteredTickets.filter(t => t.priority === 'urgent').length})</TabsTrigger>
          <TabsTrigger value="unassigned">Unassigned ({filteredTickets.filter(t => !t.assigned_to).length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <div className="space-y-4">
            {filteredTickets.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-gray-500">No tickets found matching your criteria</p>
                </CardContent>
              </Card>
            ) : (
              filteredTickets.map((ticket) => (
                <Card key={ticket.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1">{ticket.subject}</h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>From: {ticket.user_email}</span>
                          <span>Category: {ticket.category}</span>
                          <span>Messages: {ticket.messages.length}</span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Badge className={getPriorityColor(ticket.priority)}>
                          {ticket.priority}
                        </Badge>
                        <Badge className={getStatusColor(ticket.status)}>
                          {ticket.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div className="text-xs text-gray-500">
                        <span>Created: {formatTimestamp(ticket.created_at)}</span>
                        {ticket.updated_at !== ticket.created_at && (
                          <span className="ml-4">Updated: {formatTimestamp(ticket.updated_at)}</span>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Select onValueChange={(value) => handleUpdateStatus(ticket.id, value as 'open' | 'in_progress' | 'resolved' | 'closed')}>
                          <SelectTrigger className="w-[120px] h-8">
                            <SelectValue placeholder="Change Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="open">Open</SelectItem>
                            <SelectItem value="in_progress">In Progress</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="closed">Closed</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleViewTicket(ticket.id)}
                        >
                          View & Reply
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="urgent">
          <div className="space-y-4">
            {filteredTickets.filter(t => t.priority === 'urgent').map((ticket) => (
              <Card key={ticket.id} className="cursor-pointer hover:shadow-md transition-shadow border-red-200">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-1">{ticket.subject}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>From: {ticket.user_email}</span>
                        <span>Category: {ticket.category}</span>
                        <span>Messages: {ticket.messages.length}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={getPriorityColor(ticket.priority)}>
                        {ticket.priority}
                      </Badge>
                      <Badge className={getStatusColor(ticket.status)}>
                        {ticket.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      <span>Created: {formatTimestamp(ticket.created_at)}</span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewTicket(ticket.id)}
                    >
                      View & Reply
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="unassigned">
          <div className="space-y-4">
            {filteredTickets.filter(t => !t.assigned_to).map((ticket) => (
              <Card key={ticket.id} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg mb-1">{ticket.subject}</h3>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>From: {ticket.user_email}</span>
                        <span>Category: {ticket.category}</span>
                        <span>Messages: {ticket.messages.length}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge className={getPriorityColor(ticket.priority)}>
                        {ticket.priority}
                      </Badge>
                      <Badge className={getStatusColor(ticket.status)}>
                        {ticket.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      <span>Created: {formatTimestamp(ticket.created_at)}</span>
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewTicket(ticket.id)}
                    >
                      Assign & Reply
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Ticket Details Modal */}
      <Dialog open={isTicketModalOpen} onOpenChange={setIsTicketModalOpen}>
        <DialogContent className="max-w-5xl h-[85vh]">
          <DialogHeader>
            <div className="flex justify-between items-start">
              <div>
                <DialogTitle className="text-xl">{selectedTicket?.subject}</DialogTitle>
                <div className="flex gap-2 mt-2 mb-2">
                  <Badge className={getPriorityColor(selectedTicket?.priority || '')}>
                    {selectedTicket?.priority}
                  </Badge>
                  <Badge className={getStatusColor(selectedTicket?.status || '')}>
                    {selectedTicket?.status.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  <p>From: {selectedTicket?.user_email}</p>
                  <p>Category: {selectedTicket?.category}</p>
                  <p>Created: {selectedTicket && formatTimestamp(selectedTicket.created_at)}</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Select onValueChange={(value) => selectedTicket && handleUpdateStatus(selectedTicket.id, value as 'open' | 'in_progress' | 'resolved' | 'closed')}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Change Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </DialogHeader>
          
          {selectedTicket && (
            <div className="flex flex-col h-full">
              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4 pr-2 border rounded-lg p-4 bg-gray-50">
                {selectedTicket.messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender_type === "user" ? "justify-end" : "justify-start"}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg p-3 ${
                        message.sender_type === "user"
                          ? "bg-blue-100 text-blue-900"
                          : "bg-white text-gray-900 border"
                      }`}
                    >
                      <p className="text-sm whitespace-pre-wrap">{message.message}</p>
                      <span className="text-xs mt-1 block text-gray-500">
                        {message.sender_type === 'admin' ? 'Support Team' : 'User'} • {formatTimestamp(message.created_at)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Reply Area */}
              <div className="border-t pt-4">
                <div className="space-y-3">
                  <Textarea
                    placeholder="Type your response to the user..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    className="min-h-[100px]"
                    disabled={isSending}
                  />
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      This message will be sent to {selectedTicket.user_email}
                    </div>
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={isSending || !newMessage.trim()}
                    >
                      {isSending ? 'Sending...' : 'Send Reply'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 