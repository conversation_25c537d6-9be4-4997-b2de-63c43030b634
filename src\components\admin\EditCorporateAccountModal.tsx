"use client";

import React, { useState, useEffect } from 'react';
import { CorporateAccount } from '@/lib/types';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { updateCorporateAccount } from '@/lib/auth-supabase';

interface Props {
  account: CorporateAccount | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

export default function EditCorporateAccountModal({ account, isOpen, onClose, onSave }: Props) {
  const [formData, setFormData] = useState<Partial<CorporateAccount>>({});
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (account) {
      setFormData(account);
    }
  }, [account]);

  if (!account) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.id]: e.target.value });
  };

  const handleSave = async () => {
    setIsSaving(true);
    // Convert null values to undefined before sending
    const updateData = {
        organization_name: formData.organization_name || undefined,
        office_address: formData.office_address || undefined,
        contact_person: formData.contact_person || undefined,
        contact_phone: formData.contact_phone || undefined,
        business_type: formData.business_type || undefined,
        industry: formData.industry || undefined,
    };

    const result = await updateCorporateAccount(account.user_id, updateData);

    if (result.success) {
      alert("Account updated successfully");
      onSave();
      onClose();
    } else {
      alert(`Failed to update account: ${result.error}`);
    }
    setIsSaving(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit Corporate Account</DialogTitle>
        </DialogHeader>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <div>
                <Label htmlFor="organization_name">Organization Name</Label>
                <Input id="organization_name" value={formData.organization_name || ''} onChange={handleChange} />
            </div>
            <div>
                <Label htmlFor="contact_person">Contact Person</Label>
                <Input id="contact_person" value={formData.contact_person || ''} onChange={handleChange} />
            </div>
            <div className="md:col-span-2">
                <Label htmlFor="office_address">Office Address</Label>
                <Input id="office_address" value={formData.office_address || ''} onChange={handleChange} />
            </div>
            <div>
                <Label htmlFor="contact_phone">Contact Phone</Label>
                <Input id="contact_phone" value={formData.contact_phone || ''} onChange={handleChange} />
            </div>
            <div>
                <Label htmlFor="business_type">Business Type</Label>
                <Input id="business_type" value={formData.business_type || ''} onChange={handleChange} />
            </div>
             <div>
                <Label htmlFor="industry">Industry</Label>
                <Input id="industry" value={formData.industry || ''} onChange={handleChange} />
            </div>
        </div>
        <DialogFooter className="mt-6">
            <Button variant="outline" onClick={onClose}>Cancel</Button>
            <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 