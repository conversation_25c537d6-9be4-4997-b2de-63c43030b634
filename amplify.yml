version: 1
backend:
  phases:
    build:
      commands:
        - '# Execute Amplify C<PERSON><PERSON> with the helper script'
        - envCache --set stackInfo ""
        - amplifyPush --simple
frontend:
  phases:
    preBuild:
      commands:
        # Validate required environment variables
        - |
          if [ -z "$PAYSTACK_SECRET_KEY" ]; then
            echo "❌ PAYSTACK_SECRET_KEY is not set"
            exit 1
          fi
        - |
          if [ -z "$NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY" ]; then
            echo "❌ NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY is not set"
            exit 1
          fi
        - |
          if [ -z "$NEXT_PUBLIC_APP_URL" ]; then
            echo "❌ NEXT_PUBLIC_APP_URL is not set"
            exit 1
          fi
        - echo "✅ All required environment variables are set"
        # Install dependencies with better caching
        - npm ci --cache .npm --prefer-offline --no-audit
    build:
      commands:
        - echo "🏗️ Building application..."
        - npm run build
        - echo "✅ Build completed successfully"
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - .next/cache/**/*
      - .npm/**/*
      - node_modules/**/* 