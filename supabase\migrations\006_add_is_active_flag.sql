-- Add is_active column to individual_accounts
ALTER TABLE public.individual_accounts
ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;

-- Add is_active column to corporate_accounts
ALTER TABLE public.corporate_accounts
ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;

-- Create a function to toggle the active status of an individual account
CREATE OR REPLACE FUNCTION public.set_individual_account_active(
    p_user_id UUID,
    p_is_active BOOLEAN
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.individual_accounts
    SET is_active = p_is_active
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to toggle the active status of a corporate account
CREATE OR REPLACE FUNCTION public.set_corporate_account_active(
    p_user_id UUID,
    p_is_active BOOLEAN
)
RETURNS VOID AS $$
BEGIN
    UPDATE public.corporate_accounts
    SET is_active = p_is_active
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission on the new functions to the authenticated role
GRANT EXECUTE ON FUNCTION public.set_individual_account_active(UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_corporate_account_active(UUID, BOOLEAN) TO authenticated; 