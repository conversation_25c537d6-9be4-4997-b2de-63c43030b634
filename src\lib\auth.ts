// Fake authentication API functions

export interface User {
  id: string;
  email: string;
  name: string;
  type: 'individual' | 'corporate';
  companyName?: string;
}

export interface IndividualAccount {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  dateOfBirth: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  employment: {
    employer: string;
    position: string;
    monthlyIncome: number;
    employmentType: 'full-time' | 'part-time' | 'self-employed' | 'unemployed';
  };
  guarantor: {
    name: string;
    relationship: string;
    phone: string;
    email: string;
  };
  documents: {
    id: string;
    name: string;
    type: string;
    status: 'pending' | 'uploaded' | 'verified' | 'rejected';
    required: boolean;
  }[];
}

export interface CorporateAccount {
  id: string;
  email: string;
  companyName: string;
  registrationNumber: string;
  taxId: string;
  industry: string;
  foundedYear: number;
  employeeCount: number;
  contact: {
    phone: string;
    website: string;
    address: {
      street: string;
      city: string;
      state: string;
      zipCode: string;
      country: string;
    };
  };
  financial: {
    annualRevenue: number;
    monthlyRevenue: number;
    bankName: string;
    accountNumber: string;
  };
  documents: {
    id: string;
    name: string;
    type: string;
    status: 'pending' | 'uploaded' | 'verified' | 'rejected';
    required: boolean;
  }[];
}

// Fake user database
const fakeUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'John Doe',
    type: 'individual'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Jane Smith',
    type: 'individual'
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Admin User',
    type: 'corporate',
    companyName: 'TechCorp Solutions'
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Finance Manager',
    type: 'corporate',
    companyName: 'MegaBank Financial'
  },
  {
    id: '5',
    email: '<EMAIL>',
    name: 'HR Director',
    type: 'corporate',
    companyName: 'Startup Inc'
  }
];

// Fake individual accounts database
const fakeIndividualAccounts: IndividualAccount[] = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '+****************',
    dateOfBirth: '1985-03-15',
    address: {
      street: '123 Main Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA'
    },
    employment: {
      employer: 'Tech Solutions Inc',
      position: 'Software Engineer',
      monthlyIncome: 8500,
      employmentType: 'full-time'
    },
    guarantor: {
      name: 'Jane Doe',
      relationship: 'Spouse',
      phone: '+****************',
      email: '<EMAIL>'
    },
    documents: [
      { id: '1', name: 'Government ID', type: 'identity', status: 'verified', required: true },
      { id: '2', name: 'Proof of Address', type: 'address', status: 'uploaded', required: true },
      { id: '3', name: 'Employment Letter', type: 'employment', status: 'pending', required: true },
      { id: '4', name: 'Bank Statements', type: 'financial', status: 'pending', required: true },
      { id: '5', name: 'Tax Returns', type: 'financial', status: 'pending', required: false }
    ]
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    phone: '+****************',
    dateOfBirth: '1990-07-22',
    address: {
      street: '456 Oak Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      country: 'USA'
    },
    employment: {
      employer: 'Marketing Pro',
      position: 'Marketing Manager',
      monthlyIncome: 7200,
      employmentType: 'full-time'
    },
    guarantor: {
      name: 'Robert Smith',
      relationship: 'Parent',
      phone: '+****************',
      email: '<EMAIL>'
    },
    documents: [
      { id: '1', name: 'Government ID', type: 'identity', status: 'verified', required: true },
      { id: '2', name: 'Proof of Address', type: 'address', status: 'verified', required: true },
      { id: '3', name: 'Employment Letter', type: 'employment', status: 'verified', required: true },
      { id: '4', name: 'Bank Statements', type: 'financial', status: 'uploaded', required: true },
      { id: '5', name: 'Tax Returns', type: 'financial', status: 'pending', required: false }
    ]
  }
];

// Fake corporate accounts database
const fakeCorporateAccounts: CorporateAccount[] = [
  {
    id: '3',
    email: '<EMAIL>',
    companyName: 'TechCorp Solutions',
    registrationNumber: 'TC123456789',
    taxId: '12-3456789',
    industry: 'Technology',
    foundedYear: 2018,
    employeeCount: 150,
    contact: {
      phone: '+****************',
      website: 'www.techcorp.com',
      address: {
        street: '789 Tech Boulevard',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94105',
        country: 'USA'
      }
    },
    financial: {
      annualRevenue: ********,
      monthlyRevenue: 2083333,
      bankName: 'Silicon Valley Bank',
      accountNumber: '****1234'
    },
    documents: [
      { id: '1', name: 'Business Registration', type: 'legal', status: 'verified', required: true },
      { id: '2', name: 'Tax Certificate', type: 'legal', status: 'verified', required: true },
      { id: '3', name: 'Financial Statements', type: 'financial', status: 'uploaded', required: true },
      { id: '4', name: 'Bank Statements', type: 'financial', status: 'verified', required: true },
      { id: '5', name: 'Insurance Certificate', type: 'insurance', status: 'pending', required: false }
    ]
  },
  {
    id: '4',
    email: '<EMAIL>',
    companyName: 'MegaBank Financial',
    registrationNumber: 'MB987654321',
    taxId: '98-7654321',
    industry: 'Financial Services',
    foundedYear: 1995,
    employeeCount: 2500,
    contact: {
      phone: '+****************',
      website: 'www.megabank.com',
      address: {
        street: '321 Finance Street',
        city: 'New York',
        state: 'NY',
        zipCode: '10005',
        country: 'USA'
      }
    },
    financial: {
      annualRevenue: *********,
      monthlyRevenue: ********,
      bankName: 'Federal Reserve Bank',
      accountNumber: '****5678'
    },
    documents: [
      { id: '1', name: 'Business Registration', type: 'legal', status: 'verified', required: true },
      { id: '2', name: 'Tax Certificate', type: 'legal', status: 'verified', required: true },
      { id: '3', name: 'Financial Statements', type: 'financial', status: 'verified', required: true },
      { id: '4', name: 'Bank Statements', type: 'financial', status: 'verified', required: true },
      { id: '5', name: 'Insurance Certificate', type: 'insurance', status: 'verified', required: false }
    ]
  },
  {
    id: '5',
    email: '<EMAIL>',
    companyName: 'Startup Inc',
    registrationNumber: 'SI456789123',
    taxId: '45-6789123',
    industry: 'E-commerce',
    foundedYear: 2020,
    employeeCount: 25,
    contact: {
      phone: '+****************',
      website: 'www.startupinc.com',
      address: {
        street: '654 Startup Lane',
        city: 'Austin',
        state: 'TX',
        zipCode: '73301',
        country: 'USA'
      }
    },
    financial: {
      annualRevenue: 5000000,
      monthlyRevenue: 416667,
      bankName: 'Local Community Bank',
      accountNumber: '****9012'
    },
    documents: [
      { id: '1', name: 'Business Registration', type: 'legal', status: 'uploaded', required: true },
      { id: '2', name: 'Tax Certificate', type: 'legal', status: 'pending', required: true },
      { id: '3', name: 'Financial Statements', type: 'financial', status: 'pending', required: true },
      { id: '4', name: 'Bank Statements', type: 'financial', status: 'uploaded', required: true },
      { id: '5', name: 'Insurance Certificate', type: 'insurance', status: 'pending', required: false }
    ]
  }
];

// Corporate email domains that are allowed
const allowedCorporateDomains = [
  'techcorp.com',
  'megabank.com',
  'startupinc.com',
  'enterprise.com',
  'business.com',
  'corporate.com',
  'company.com',
  'firm.com',
  'ltd.com',
  'inc.com'
];

// Public email domains that are not allowed for corporate users
const publicEmailDomains = [
  'gmail.com',
  'yahoo.com',
  'outlook.com',
  'hotmail.com',
  'aol.com',
  'icloud.com',
  'protonmail.com',
  'mail.com',
  'live.com',
  'msn.com'
];

export const validateCorporateEmail = (email: string): boolean => {
  const domain = email.split('@')[1]?.toLowerCase();
  if (!domain) return false;
  
  // Check if it's a public email domain
  if (publicEmailDomains.includes(domain)) {
    return false;
  }
  
  // Check if it's an allowed corporate domain
  if (allowedCorporateDomains.includes(domain)) {
    return true;
  }
  
  // For other domains, we'll allow them but show a warning
  return true;
};

export const fakeLogin = async (email: string, password: string, userType: 'individual' | 'corporate'): Promise<{ success: boolean; user?: User; error?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Find user by email
  const user = fakeUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
  
  if (!user) {
    return {
      success: false,
      error: 'Invalid email or password'
    };
  }
  
  // Check if user type matches
  if (user.type !== userType) {
    return {
      success: false,
      error: userType === 'corporate' 
        ? 'This email is not registered as a corporate account' 
        : 'This email is not registered as an individual account'
    };
  }
  
  // For corporate users, validate email domain
  if (userType === 'corporate') {
    if (!validateCorporateEmail(email)) {
      return {
        success: false,
        error: 'Corporate accounts cannot use public email domains like Gmail, Yahoo, or Outlook. Please use your company email address.'
      };
    }
  }
  
  // Fake password validation (any password works for demo)
  if (password.length < 6) {
    return {
      success: false,
      error: 'Password must be at least 6 characters long'
    };
  }
  
  return {
    success: true,
    user
  };
};

export const getDashboardRedirect = (userType: 'individual' | 'corporate'): string => {
  return userType === 'individual' ? '/dashboard/borrower' : '/dashboard/lender';
};

// Fake API to fetch individual account information
export const fetchIndividualAccount = async (userId: string): Promise<{ success: boolean; account?: IndividualAccount; error?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const account = fakeIndividualAccounts.find(acc => acc.id === userId);
  
  if (!account) {
    return {
      success: false,
      error: 'Account not found'
    };
  }
  
  return {
    success: true,
    account
  };
};

// Fake API to fetch corporate account information
export const fetchCorporateAccount = async (userId: string): Promise<{ success: boolean; account?: CorporateAccount; error?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  const account = fakeCorporateAccounts.find(acc => acc.id === userId);
  
  if (!account) {
    return {
      success: false,
      error: 'Account not found'
    };
  }
  
  return {
    success: true,
    account
  };
};

// Fake API to update individual account information
export const updateIndividualAccount = async (userId: string, updates: Partial<IndividualAccount>): Promise<{ success: boolean; account?: IndividualAccount; error?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const accountIndex = fakeIndividualAccounts.findIndex(acc => acc.id === userId);
  
  if (accountIndex === -1) {
    return {
      success: false,
      error: 'Account not found'
    };
  }
  
  // Update the account
  fakeIndividualAccounts[accountIndex] = {
    ...fakeIndividualAccounts[accountIndex],
    ...updates
  };
  
  return {
    success: true,
    account: fakeIndividualAccounts[accountIndex]
  };
};

// Fake API to update corporate account information
export const updateCorporateAccount = async (userId: string, updates: Partial<CorporateAccount>): Promise<{ success: boolean; account?: CorporateAccount; error?: string }> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  const accountIndex = fakeCorporateAccounts.findIndex(acc => acc.id === userId);
  
  if (accountIndex === -1) {
    return {
      success: false,
      error: 'Account not found'
    };
  }
  
  // Update the account
  fakeCorporateAccounts[accountIndex] = {
    ...fakeCorporateAccounts[accountIndex],
    ...updates
  };
  
  return {
    success: true,
    account: fakeCorporateAccounts[accountIndex]
  };
}; 