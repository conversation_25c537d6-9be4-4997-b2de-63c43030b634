"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  CreditCard, 
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Download
} from 'lucide-react';
import {
  getLoanPaymentHistory,
  formatCurrency,
  formatDate,
  type LoanPayment
} from '@/lib/payments';
import { toast } from 'sonner';

interface TransactionHistoryProps {
  loanApplicationId: string;
  onRefresh?: () => void;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({
  loanApplicationId
}) => {
  const [payments, setPayments] = useState<LoanPayment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPayment, setSelectedPayment] = useState<LoanPayment | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const fetchPaymentHistory = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await getLoanPaymentHistory(loanApplicationId);
      
      if (result.success && result.data) {
        setPayments(result.data);
      } else {
        toast.error(result.error || 'Failed to fetch payment history');
      }
    } catch (error) {
      console.error('Error fetching payment history:', error);
      toast.error('An error occurred while fetching payment history');
    } finally {
      setIsLoading(false);
    }
  }, [loanApplicationId]);

  useEffect(() => {
    fetchPaymentHistory();
  }, [loanApplicationId, fetchPaymentHistory]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'failed':
        return <XCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getPaymentTypeLabel = (type: string) => {
    switch (type) {
      case 'regular':
        return 'Regular Payment';
      case 'principal_only':
        return 'Principal Only';
      case 'early_payment':
        return 'Early Payment';
      case 'late_payment':
        return 'Late Payment';
      default:
        return 'Payment';
    }
  };

  const filteredPayments = payments.filter(payment => 
    statusFilter === 'all' || payment.status === statusFilter
  );

  const totalPaid = payments
    .filter(p => p.status === 'completed')
    .reduce((sum, p) => sum + p.payment_amount, 0);

  const pendingAmount = payments
    .filter(p => p.status === 'pending')
    .reduce((sum, p) => sum + p.payment_amount, 0);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Transaction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Paid</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(totalPaid)}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Amount</p>
                <p className="text-2xl font-bold text-yellow-600">{formatCurrency(pendingAmount)}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Transactions</p>
                <p className="text-2xl font-bold">{payments.length}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Transaction History
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStatusFilter('all')}
                className={statusFilter === 'all' ? 'bg-blue-50 border-blue-200' : ''}
              >
                All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStatusFilter('completed')}
                className={statusFilter === 'completed' ? 'bg-green-50 border-green-200' : ''}
              >
                Completed
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStatusFilter('pending')}
                className={statusFilter === 'pending' ? 'bg-yellow-50 border-yellow-200' : ''}
              >
                Pending
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchPaymentHistory}
              >
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredPayments.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No Transactions Found</h3>
              <p className="text-gray-500">
                {statusFilter === 'all' 
                  ? 'No payments have been made for this loan yet.'
                  : `No ${statusFilter} transactions found.`
                }
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredPayments.map((payment) => (
                <div key={payment.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold">{getPaymentTypeLabel(payment.payment_type)}</h3>
                        <Badge className={`${getStatusColor(payment.status)} flex items-center gap-1`}>
                          {getStatusIcon(payment.status)}
                          {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500">
                        {payment.payment_date ? formatDate(payment.payment_date) : formatDate(payment.created_at)}
                        {payment.scheduled_payment_month && (
                          <span className="ml-2">• Payment #{payment.scheduled_payment_month}</span>
                        )}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xl font-bold">{formatCurrency(payment.payment_amount)}</p>
                      <p className="text-sm text-gray-500">
                        Ref: {payment.payment_reference?.slice(-8) || 'N/A'}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3 text-sm">
                    <div>
                      <p className="text-gray-500">Principal</p>
                      <p className="font-medium">{formatCurrency(payment.principal_amount)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Interest</p>
                      <p className="font-medium">{formatCurrency(payment.interest_amount)}</p>
                    </div>
                    {payment.processing_fee > 0 && (
                      <div>
                        <p className="text-gray-500">Processing Fee</p>
                        <p className="font-medium text-orange-600">{formatCurrency(payment.processing_fee)}</p>
                      </div>
                    )}
                    {payment.late_fee > 0 && (
                      <div>
                        <p className="text-gray-500">Late Fee</p>
                        <p className="font-medium text-red-600">{formatCurrency(payment.late_fee)}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-400">
                      Payment ID: {payment.id.slice(0, 8)}...
                    </span>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedPayment(payment)}
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View Details
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Payment Details</DialogTitle>
                        </DialogHeader>
                        {selectedPayment && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-gray-500">Payment Type</p>
                                <p className="font-medium">{getPaymentTypeLabel(selectedPayment.payment_type)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Status</p>
                                <Badge className={getStatusColor(selectedPayment.status)}>
                                  {selectedPayment.status.charAt(0).toUpperCase() + selectedPayment.status.slice(1)}
                                </Badge>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Payment Date</p>
                                <p className="font-medium">
                                  {selectedPayment.payment_date ? formatDate(selectedPayment.payment_date) : 'Not completed'}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Due Date</p>
                                <p className="font-medium">
                                  {selectedPayment.due_date ? formatDate(selectedPayment.due_date) : 'N/A'}
                                </p>
                              </div>
                            </div>

                            <div className="border-t pt-4">
                              <h3 className="font-semibold mb-3">Payment Breakdown</h3>
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span>Principal Amount:</span>
                                  <span className="font-medium">{formatCurrency(selectedPayment.principal_amount)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Interest Amount:</span>
                                  <span className="font-medium">{formatCurrency(selectedPayment.interest_amount)}</span>
                                </div>
                                {selectedPayment.processing_fee > 0 && (
                                  <div className="flex justify-between">
                                    <span>Processing Fee:</span>
                                    <span className="font-medium text-orange-600">{formatCurrency(selectedPayment.processing_fee)}</span>
                                  </div>
                                )}
                                {selectedPayment.late_fee > 0 && (
                                  <div className="flex justify-between">
                                    <span>Late Fee:</span>
                                    <span className="font-medium text-red-600">{formatCurrency(selectedPayment.late_fee)}</span>
                                  </div>
                                )}
                                <div className="border-t pt-2">
                                  <div className="flex justify-between text-lg font-bold">
                                    <span>Total Amount:</span>
                                    <span>{formatCurrency(selectedPayment.payment_amount)}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="border-t pt-4">
                              <h3 className="font-semibold mb-3">Transaction Information</h3>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span>Payment Reference:</span>
                                  <span className="font-mono">{selectedPayment.payment_reference || 'N/A'}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Payment Method:</span>
                                  <span className="capitalize">{selectedPayment.payment_method}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span>Transaction ID:</span>
                                  <span className="font-mono">{selectedPayment.id}</span>
                                </div>
                                {selectedPayment.notes && (
                                  <div className="mt-3">
                                    <p className="text-sm text-gray-500">Notes:</p>
                                    <p className="text-sm">{selectedPayment.notes}</p>
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="flex gap-2 pt-4 border-t">
                              <Button variant="outline" className="flex-1">
                                <Download className="w-4 h-4 mr-2" />
                                Download Receipt
                              </Button>
                              {selectedPayment.status === 'completed' && (
                                <Button variant="outline" className="flex-1">
                                  <FileText className="w-4 h-4 mr-2" />
                                  View Statement
                                </Button>
                              )}
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TransactionHistory; 