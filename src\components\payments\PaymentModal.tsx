"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { 
  CreditCard, 
  Calendar,
  Loader2,
  Info,
  Calculator
} from 'lucide-react';
import {
  initializePaystackPayment,
  createLoanPayment,
  createPaymentTransaction,
  getNextPaymentDue,
  calculatePaymentBreakdown,
  formatCurrency,
  formatDate,
  type PaymentSchedule
} from '@/lib/payments';

interface LoanApplication {
  id: string;
  requested_amount: number;
  requested_duration: number;
  full_name: string;
  email: string;
  loan_offers?: {
    product_name: string;
    interest_rate: number;
    rate_type: string;
    processing_fee: number;
  };
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  loan: LoanApplication;
  onPaymentSuccess?: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  loan
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('quick-pay');
  const [paymentAmount, setPaymentAmount] = useState('');
  const [customAmount, setCustomAmount] = useState('');
  const [paymentType, setPaymentType] = useState<'regular' | 'principal_only' | 'early_payment' | 'late_payment'>('regular');
  const [nextPayment, setNextPayment] = useState<PaymentSchedule | null>(null);
  const [paymentBreakdown, setPaymentBreakdown] = useState<{
    principalAmount: number;
    interestAmount: number;
    processingFee: number;
    totalAmount: number;
  } | null>(null);

  const fetchNextPayment = useCallback(async () => {
    try {
      const result = await getNextPaymentDue(loan.id);
      if (result.success && result.data) {
        setNextPayment(result.data);
        setPaymentAmount(result.data.expected_payment_amount.toString());
      }
    } catch (error) {
      console.error('Error fetching next payment:', error);
    }
  }, [loan.id]);

  // Fetch next payment due when modal opens
  useEffect(() => {
    if (isOpen && loan.id) {
      fetchNextPayment();
    }
  }, [isOpen, loan.id, fetchNextPayment]);

  // Update payment breakdown when amount changes
  useEffect(() => {
    const amount = parseFloat(paymentAmount || customAmount);
    if (amount && loan.requested_amount && loan.loan_offers?.interest_rate) {
      const breakdown = calculatePaymentBreakdown(
        amount,
        loan.requested_amount, // This should be current outstanding balance
        loan.loan_offers.interest_rate,
        activeTab === 'quick-pay' && nextPayment?.month_number === 1 ? 
          (loan.requested_amount * (loan.loan_offers.processing_fee / 100)) : 0
      );
      setPaymentBreakdown(breakdown);
    } else {
      setPaymentBreakdown(null);
    }
  }, [paymentAmount, customAmount, loan, nextPayment, activeTab]);

  const handlePayment = async () => {
    try {
      setIsLoading(true);

      const amount = parseFloat(paymentAmount || customAmount);
      if (!amount || amount <= 0) {
        toast.error('Please enter a valid payment amount');
        return;
      }

      if (!loan.email) {
        toast.error('Email is required for payment processing');
        return;
      }

      // Initialize Paystack payment
      const paystackResult = await initializePaystackPayment({
        amount,
        email: loan.email,
        loanApplicationId: loan.id,
        scheduledPaymentMonth: nextPayment?.month_number,
        paymentType
      });

      if (!paystackResult.status || !paystackResult.data) {
        toast.error(paystackResult.message || 'Failed to initialize payment');
        return;
      }

      // Create loan payment record
      const paymentResult = await createLoanPayment(loan.id, {
        amount,
        paymentType,
        scheduledPaymentMonth: nextPayment?.month_number,
        principalAmount: paymentBreakdown?.principalAmount || 0,
        interestAmount: paymentBreakdown?.interestAmount || 0,
        processingFee: paymentBreakdown?.processingFee || 0,
        paymentReference: paystackResult.data.reference
      });

      if (!paymentResult.success || !paymentResult.data) {
        toast.error('Failed to create payment record');
        return;
      }

      // Create transaction record
      await createPaymentTransaction(paymentResult.data.id, {
        paystackReference: paystackResult.data.reference,
        amount,
        email: loan.email,
        accessCode: paystackResult.data.access_code
      });

      // Redirect to Paystack
      window.location.href = paystackResult.data.authorization_url;

    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error('An error occurred while processing payment');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateMonthlyPayment = () => {
    if (!loan.requested_amount || !loan.loan_offers?.interest_rate || !loan.requested_duration) {
      return 0;
    }

    const principal = loan.requested_amount;
    const monthlyRate = loan.loan_offers.interest_rate / 100 / 12;
    const monthlyPayment = principal * (monthlyRate * Math.pow(1 + monthlyRate, loan.requested_duration)) / 
                          (Math.pow(1 + monthlyRate, loan.requested_duration) - 1);
    
    return monthlyPayment;
  };

  const suggestedAmounts = [
    { label: 'Next Payment', amount: nextPayment?.expected_payment_amount || calculateMonthlyPayment() },
    { label: 'Double Payment', amount: (nextPayment?.expected_payment_amount || calculateMonthlyPayment()) * 2 },
    { label: 'Half Payment', amount: (nextPayment?.expected_payment_amount || calculateMonthlyPayment()) / 2 },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Make Loan Payment
          </DialogTitle>
          <DialogDescription>
            Make a payment for your loan: {loan.loan_offers?.product_name || 'Personal Loan'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Loan Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Loan Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Principal Amount</p>
                  <p className="font-semibold">{formatCurrency(loan.requested_amount)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Interest Rate</p>
                  <p className="font-semibold">{loan.loan_offers?.interest_rate}% {loan.loan_offers?.rate_type}</p>
                </div>
                <div>
                  <p className="text-gray-500">Duration</p>
                  <p className="font-semibold">{loan.requested_duration} months</p>
                </div>
                <div>
                  <p className="text-gray-500">Monthly Payment</p>
                  <p className="font-semibold">{formatCurrency(calculateMonthlyPayment())}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Next Payment Due */}
          {nextPayment && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-blue-800 flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  Next Payment Due
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-blue-600">Due Date</p>
                    <p className="font-semibold text-blue-800">{formatDate(nextPayment.due_date)}</p>
                  </div>
                  <div>
                    <p className="text-blue-600">Amount Due</p>
                    <p className="font-semibold text-blue-800">{formatCurrency(nextPayment.expected_payment_amount)}</p>
                  </div>
                  <div>
                    <p className="text-blue-600">Payment #{nextPayment.month_number}</p>
                    <Badge className="bg-blue-100 text-blue-800">
                      {nextPayment.status.charAt(0).toUpperCase() + nextPayment.status.slice(1)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Options */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="quick-pay">Quick Pay</TabsTrigger>
              <TabsTrigger value="custom-pay">Custom Amount</TabsTrigger>
            </TabsList>

            <TabsContent value="quick-pay" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {suggestedAmounts.map((option, index) => (
                  <Card 
                    key={index}
                    className={`cursor-pointer transition-colors ${
                      paymentAmount === option.amount.toString() 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'hover:border-gray-300'
                    }`}
                    onClick={() => setPaymentAmount(option.amount.toString())}
                  >
                    <CardContent className="p-4 text-center">
                      <p className="text-sm text-gray-600">{option.label}</p>
                      <p className="text-lg font-semibold">{formatCurrency(option.amount)}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="custom-pay" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customAmount">Payment Amount</Label>
                  <Input
                    id="customAmount"
                    type="number"
                    placeholder="Enter amount"
                    value={customAmount}
                    onChange={(e) => setCustomAmount(e.target.value)}
                    min="1"
                    step="0.01"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="paymentType">Payment Type</Label>
                  <Select value={paymentType} onValueChange={(value: 'regular' | 'principal_only' | 'early_payment' | 'late_payment') => setPaymentType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="regular">Regular Payment</SelectItem>
                      <SelectItem value="early_payment">Early Payment</SelectItem>
                      <SelectItem value="principal_only">Principal Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Payment Breakdown */}
          {paymentBreakdown && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Calculator className="w-4 h-4" />
                  Payment Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Principal Amount:</span>
                    <span className="font-semibold">{formatCurrency(paymentBreakdown.principalAmount)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Interest Amount:</span>
                    <span className="font-semibold">{formatCurrency(paymentBreakdown.interestAmount)}</span>
                  </div>
                  {paymentBreakdown.processingFee > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Processing Fee:</span>
                      <span className="font-semibold text-orange-600">{formatCurrency(paymentBreakdown.processingFee)}</span>
                    </div>
                  )}
                  <div className="border-t pt-2">
                    <div className="flex justify-between">
                      <span className="font-medium">Total Amount:</span>
                      <span className="font-bold text-lg">{formatCurrency(paymentBreakdown.totalAmount)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <Info className="w-5 h-5 text-blue-500 mt-0.5" />
              <div className="space-y-2 text-sm text-gray-600">
                <p>
                  <strong>Secure Payment:</strong> Your payment is processed securely through Paystack.
                </p>
                <p>
                  <strong>Payment Methods:</strong> Credit/Debit cards, Bank transfers, USSD, and mobile money are supported.
                </p>
                <p>
                  <strong>Confirmation:</strong> You&apos;ll receive a confirmation email once payment is successful.
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handlePayment}
              className="flex-1 bg-green-600 hover:bg-green-700"
              disabled={isLoading || (!paymentAmount && !customAmount)}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4 mr-2" />
                  Pay {paymentBreakdown ? formatCurrency(paymentBreakdown.totalAmount) : 'Now'}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal; 