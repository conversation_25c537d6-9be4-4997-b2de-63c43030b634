import React from "react";
import Image from "next/image";
import Link from "next/link";

const Hero = () => {
  return (
    <div
      style={{
        backgroundImage: "url('/hero-background.png')",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "center top",
        backgroundSize: "90% auto",
      }}
      className="w-full"
    >
      <div className="px-4 md:px-16 pt-6">
        <section className="grid grid-cols-1 md:grid-cols-2 gap-8 py-8 md:py-12">
          <div className="flex flex-col justify-center /80 p-4 md:p-8 rounded-lg">
            <div className="flex gap-4 mb-4">
              <span className="flex items-center gap-1 text-sm">
                <span className="w-2 h-2 bg-black rounded-full"></span>
                Secure
              </span>
              <span className="flex items-center gap-1 text-sm">
                <span className="w-2 h-2 bg-black rounded-full"></span>
                Fast
              </span>
              <span className="flex items-center gap-1 text-sm">
                <span className="w-2 h-2 bg-black rounded-full"></span>
                Profitable
              </span>
            </div>

            <h1 className="text-2xl sm:text-3xl md:text-5xl font-bold mb-4">
              Lend and Borrow
              <br />
              Cash From Kredxa
            </h1>

            <p className="text-gray-600 mb-6 text-base sm:text-lg">
              Borrow, Lend and Manage your assets on Kredxa where vetted
              <br />
              lenders connect with borrowers
            </p>

            <div className="flex flex-col md:flex-row gap-4">
              <Link
                href="/borrow"
                className="bg-[black] text-white px-6 py-3 rounded-full text-center"
              >
                START BORROWING
              </Link>
              <Link
                href="/lend"
                className="bg-white text-black px-6 py-3 rounded-full border border-white-300 shadow-lg font-bold text-center"
              >
                START LENDING
              </Link>
            </div>
          </div>

          <div className="flex justify-center items-center mt-6 md:mt-0">
            <Image
              src="/hero.svg"
              alt="Kredxa Finance Illustration"
              width={320}
              height={320}
              className="w-full max-w-xs md:max-w-md h-auto"
              priority
            />
          </div>
        </section>
      </div>
    </div>
  );
};

export default Hero;