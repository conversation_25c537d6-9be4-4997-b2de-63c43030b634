-- Add employment and guarantor fields to individual_accounts table
ALTER TABLE individual_accounts 
ADD COLUMN IF NOT EXISTS employer TEXT,
ADD COLUMN IF NOT EXISTS position TEXT,
ADD COLUMN IF NOT EXISTS monthly_income DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS employment_type TEXT CHECK (employment_type IN ('full-time', 'part-time', 'self-employed', 'unemployed')),
ADD COLUMN IF NOT EXISTS guarantor_name TEXT,
ADD COLUMN IF NOT EXISTS guarantor_relationship TEXT,
ADD COLUMN IF NOT EXISTS guarantor_phone TEXT,
ADD COLUMN IF NOT EXISTS guarantor_email TEXT;

-- Update the updated_at trigger to include new columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Recreate the trigger for individual_accounts
DROP TRIGGER IF EXISTS update_individual_accounts_updated_at ON individual_accounts;
CREATE TRIGGER update_individual_accounts_updated_at 
  BEFORE UPDATE ON individual_accounts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 