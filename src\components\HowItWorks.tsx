import React from 'react';
import Image from 'next/image';

const HowItWorks = () => {
  return (
    <div className="py-12 sm:py-16" style={{ backgroundImage: "url('/image 6.svg')", backgroundSize: 'cover', backgroundPosition: 'center' }}>
      <div className="text-center px-4">
        <h3 className="text-base sm:text-lg font-semibold text-gray-600">Only A Trial Will Convince You That</h3>
        <h2 className="text-3xl sm:text-4xl font-bold mt-2">Kredxa The Right Platform For You</h2>
        <p className="text-gray-500 mt-4 max-w-2xl mx-auto text-sm sm:text-base">
          We help you save time and money by easily finding the best loan options. 3 simple ways to get started
        </p>
      </div>
      <div className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-6xl mx-auto px-4">
        <div className="text-center">
          <Image src="/image 12.svg" alt="Sign Up" width={100} height={100} className="mx-auto" />
          <p className="text-2xl font-bold mt-4 text-gray-400">01</p>
          <p className="font-semibold mt-2">Sign Up</p>
        </div>
        <div className="text-center">
          <Image src="/image 13.svg" alt="Compare Offers" width={100} height={100} className="mx-auto" />
          <p className="text-2xl font-bold mt-4 text-gray-400">02</p>
          <p className="font-semibold mt-2">Compare Offers</p>
        </div>
        <div className="text-center">
          <Image src="/image 14.svg" alt="Apply" width={100} height={100} className="mx-auto" />
          <p className="text-2xl font-bold mt-4 text-gray-400">03</p>
          <p className="font-semibold mt-2">Apply</p>
        </div>
        <div className="text-center">
          <Image src="/Cash in Hand.svg" alt="Disburse" width={100} height={100} className="mx-auto" />
          <p className="text-2xl font-bold mt-4 text-gray-400">04</p>
          <p className="font-semibold mt-2">Disburse</p>
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;