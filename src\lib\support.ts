import { supabase } from './supabase';

export interface SupportTicket {
  id: string;
  user_id: string;
  subject: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  assigned_to: string | null;
  created_at: string;
  updated_at: string;
  resolved_at: string | null;
}

export interface SupportMessage {
  id: string;
  ticket_id: string;
  sender_id: string;
  sender_type: 'user' | 'admin';
  message: string;
  is_internal: boolean;
  attachments: Record<string, unknown>[];
  created_at: string;
  updated_at: string;
}

export interface TicketWithMessages extends SupportTicket {
  messages: SupportMessage[];
  user_name?: string;
  user_email?: string;
}

// Create a new support ticket
export async function createSupportTicket(
  userId: string,
  subject: string,
  initialMessage: string,
  category: string = 'general',
  priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium'
): Promise<{ ticket: SupportTicket; message: SupportMessage } | null> {
  try {
    // Create the ticket
    const { data: ticketData, error: ticketError } = await supabase
      .from('support_tickets')
      .insert({
        user_id: userId,
        subject,
        category,
        priority,
        status: 'open',
      })
      .select()
      .single();

    if (ticketError) throw ticketError;

    // Create the initial message
    const { data: messageData, error: messageError } = await supabase
      .from('support_messages')
      .insert({
        ticket_id: ticketData.id,
        sender_id: userId,
        sender_type: 'user',
        message: initialMessage,
        is_internal: false,
      })
      .select()
      .single();

    if (messageError) throw messageError;

    return { ticket: ticketData, message: messageData };
  } catch (error) {
    console.error('Error creating support ticket:', error);
    return null;
  }
}

// Get user's support tickets
export async function getUserTickets(userId: string): Promise<SupportTicket[]> {
  try {
    const { data, error } = await supabase
      .from('support_tickets')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching user tickets:', error);
    return [];
  }
}

// Get a specific ticket with messages
export async function getTicketWithMessages(ticketId: string, userId?: string): Promise<TicketWithMessages | null> {
  try {
    // Get the ticket
    let ticketQuery = supabase
      .from('support_tickets')
      .select('*')
      .eq('id', ticketId);

    // If userId is provided, also filter by user (for security)
    if (userId) {
      ticketQuery = ticketQuery.eq('user_id', userId);
    }

    const { data: ticketData, error: ticketError } = await ticketQuery.single();
    if (ticketError) throw ticketError;

    // Get user email
    const { data: profileData } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', ticketData.user_id)
      .single();

    // Get the messages
    const { data: messagesData, error: messagesError } = await supabase
      .from('support_messages')
      .select('*')
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: true });

    if (messagesError) throw messagesError;

    return {
      ...ticketData,
      messages: messagesData || [],
      user_email: profileData?.email || 'Unknown',
    };
  } catch (error) {
    console.error('Error fetching ticket with messages:', error);
    return null;
  }
}

// Add a message to a ticket
export async function addMessageToTicket(
  ticketId: string,
  senderId: string,
  senderType: 'user' | 'admin',
  message: string,
  isInternal = false
): Promise<SupportMessage | null> {
  try {
    const { data, error } = await supabase
      .from('support_messages')
      .insert({
        ticket_id: ticketId,
        sender_id: senderId,
        sender_type: senderType,
        message,
        is_internal: isInternal,
      })
      .select()
      .single();

    if (error) throw error;

    // Update ticket's updated_at timestamp
    await supabase
      .from('support_tickets')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', ticketId);

    return data;
  } catch (error) {
    console.error('Error adding message to ticket:', error);
    return null;
  }
}

// Update ticket status
export async function updateTicketStatus(
  ticketId: string,
  status: 'open' | 'in_progress' | 'resolved' | 'closed',
  assignedTo?: string
): Promise<boolean> {
  try {
    const updateData: Partial<{
      status: string;
      updated_at: string;
      assigned_to: string | null;
      resolved_at: string | null;
    }> = {
      status,
      updated_at: new Date().toISOString(),
    };

    if (assignedTo !== undefined) {
      updateData.assigned_to = assignedTo;
    }

    if (status === 'resolved' || status === 'closed') {
      updateData.resolved_at = new Date().toISOString();
    }

    const { error } = await supabase
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error updating ticket status:', error);
    return false;
  }
}

// Get all tickets for admin
export async function getAllTickets(
  status?: string,
  priority?: string,
  category?: string
): Promise<TicketWithMessages[]> {
  try {
    console.log('getAllTickets called with filters:', { status, priority, category });
    
    let query = supabase
      .from('support_tickets')
      .select('*')
      .order('created_at', { ascending: false });

    if (status) query = query.eq('status', status);
    if (priority) query = query.eq('priority', priority);
    if (category) query = query.eq('category', category);

    console.log('Executing tickets query...');
    const { data: ticketsData, error: ticketsError } = await query;
    
    if (ticketsError) {
      console.error('Tickets query error:', ticketsError);
      throw ticketsError;
    }

    console.log('Raw tickets data:', ticketsData);
    console.log('Number of tickets found:', ticketsData?.length || 0);

    if (!ticketsData || ticketsData.length === 0) {
      console.log('No tickets found, returning empty array');
      return [];
    }

    // Get user emails and messages for each ticket
    console.log('Processing tickets with user details...');
    const ticketsWithDetails = await Promise.all(
      ticketsData.map(async (ticket, index) => {
        console.log(`Processing ticket ${index + 1}/${ticketsData.length}:`, ticket.id);
        
        // Get user email
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('email')
          .eq('id', ticket.user_id)
          .single();

        if (profileError) {
          console.error(`Profile error for ticket ${ticket.id}:`, profileError);
        }

        // Get messages
        const { data: messagesData, error: messagesError } = await supabase
          .from('support_messages')
          .select('*')
          .eq('ticket_id', ticket.id)
          .order('created_at', { ascending: true });

        if (messagesError) {
          console.error(`Messages error for ticket ${ticket.id}:`, messagesError);
        }

        const result = {
          ...ticket,
          messages: messagesData || [],
          user_email: profileData?.email || 'Unknown',
        };

        console.log(`Processed ticket ${ticket.id}:`, {
          subject: result.subject,
          user_email: result.user_email,
          messages_count: result.messages.length
        });

        return result;
      })
    );

    console.log('Final processed tickets:', ticketsWithDetails.length);
    return ticketsWithDetails;
  } catch (error) {
    console.error('Error fetching all tickets:', error);
    return [];
  }
}

// Get ticket statistics
export async function getTicketStats(): Promise<{
  total: number;
  open: number;
  in_progress: number;
  resolved: number;
  closed: number;
}> {
  try {
    const { data, error } = await supabase
      .from('support_tickets')
      .select('status');

    if (error) throw error;

    const stats = {
      total: data?.length || 0,
      open: 0,
      in_progress: 0,
      resolved: 0,
      closed: 0,
    };

    data?.forEach((ticket) => {
      if (ticket.status in stats) {
        stats[ticket.status as keyof typeof stats]++;
      }
    });

    return stats;
  } catch (error) {
    console.error('Error fetching ticket stats:', error);
    return { total: 0, open: 0, in_progress: 0, resolved: 0, closed: 0 };
  }
}

// Test function to check if tables exist and have data
export async function testSupportSystem(): Promise<void> {
  try {
    console.log('Testing support system...');
    
    // Test current user and admin status
    const { data: { user } } = await supabase.auth.getUser();
    console.log('Current user:', user?.id);
    
    if (user) {
      // Check if user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin, admin_level')
        .eq('id', user.id)
        .single();
      
      if (profileError) {
        console.error('Profile check error:', profileError);
      } else {
        console.log('User profile:', profile);
        console.log('Is admin:', profile?.is_admin);
      }
      
      // Check admin_users table
      const { data: adminUser, error: adminError } = await supabase
        .from('admin_users')
        .select('*')
        .eq('user_id', user.id)
        .single();
      
      if (adminError) {
        console.error('Admin users check error:', adminError);
      } else {
        console.log('Admin user record:', adminUser);
      }
    }
    
    // Test tickets table
    const { data: tickets, error: ticketsError } = await supabase
      .from('support_tickets')
      .select('*')
      .limit(5);
    
    if (ticketsError) {
      console.error('Tickets table error:', ticketsError);
    } else {
      console.log('Tickets found:', tickets?.length || 0);
      console.log('Sample tickets:', tickets);
    }
    
    // Test messages table
    const { data: messages, error: messagesError } = await supabase
      .from('support_messages')
      .select('*')
      .limit(5);
    
    if (messagesError) {
      console.error('Messages table error:', messagesError);
    } else {
      console.log('Messages found:', messages?.length || 0);
      console.log('Sample messages:', messages);
    }
    
    // Test profiles table
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, is_admin')
      .limit(5);
    
    if (profilesError) {
      console.error('Profiles table error:', profilesError);
    } else {
      console.log('Profiles found:', profiles?.length || 0);
    }
    
  } catch (error) {
    console.error('Test error:', error);
  }
} 