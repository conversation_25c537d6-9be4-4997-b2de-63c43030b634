import Image from "next/image";

const features = [
	{
		icon: "/secure-process.svg",
		title: "Secure Process",
		desc: "Your data is protected with bank-level security measures.",
	},
	{
		icon: "/risk-assessment.svg",
		title: "Risk Assessment Tools",
		desc: "Advanced analytics to evaluate risk and optimize lending portfolios.",
	},
	{
		icon: "/multiple-offers.svg",
		title: "Compare Multiple Offers",
		desc: "Access and compare loan offers from multiple lenders in one place.",
	},
	{
		icon: "/quick-approval.svg",
		title: "Quick Approval",
		desc: "Get approved faster with our streamlined application process.",
	},
	{
		icon: "/portfolio-tracking.svg",
		title: "Portfolio Tracking",
		desc: "Monitor, manage and grow your lending portfolio with ease.",
	},
	{
		icon: "/pre-vettedborrower.svg",
		title: "Pre-vetted Borrowers",
		desc: "Access to verified borrowers with complete  documentation.",
	},
];

const Features = () => (
	<section className="py-16 px-4 md:px-16 bg-white">
		<h3 className="text-sm mb-2 text-center">The Most Credible</h3>
		<h2 className="text-2xl md:text-3xl font-bold mb-2 text-center">
			Financial Loan Platform
		</h2>
		<p className="text-gray-600 mb-10 text-center">
			Here are a few reasons why Kredxa is your go to platform
		</p>
		<div className="grid grid-cols-1 md:grid-cols-2 gap-8 gap-x-20 max-w-full md:max-w-4xl mx-auto">
			<div>
				<div className="flex flex-col gap-8">
					{features.slice(0, 3).map((f) => (
						<div
							key={f.title}
							className="flex items-start gap-4 text-left"
						>
							<div className="flex justify-center md:justify-start min-w-[40px]">
								<Image
									src={f.icon}
									alt={f.title}
									width={40}
									height={40}
								/>
							</div>
							<div>
								<h4 className="font-semibold text-left">{f.title}</h4>
								<p className="text-gray-600 text-sm text-left">
									{f.desc}
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
			<div>
				<div className="flex flex-col gap-8">
					{features.slice(3).map((f) => (
						<div
							key={f.title}
							className="flex items-start gap-4 text-left"
						>
							<div className="flex justify-center md:justify-start min-w-[40px]">
								<Image
									src={f.icon}
									alt={f.title}
									width={40}
									height={40}
								/>
							</div>
							<div>
								<h4 className="font-semibold text-left">{f.title}</h4>
								<p className="text-gray-600 text-sm text-left">
									{f.desc}
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	</section>
);

export default Features;