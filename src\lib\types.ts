export interface IndividualAccount {
  id: string;
  user_id: string;
  email: string;
  full_name: string | null;
  phone_number: string | null;
  bvn: string | null;
  verification_status: 'pending' | 'verified' | 'rejected';
  employer: string | null;
  position: string | null;
  monthly_income: number | null;
  employment_type: 'full-time' | 'part-time' | 'self-employed' | 'unemployed' | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CorporateAccount {
  id: string;
  user_id: string;
  email: string;
  organization_name: string | null;
  office_address: string | null;
  contact_person: string | null;
  contact_phone: string | null;
  business_type: string | null;
  industry: string | null;
  company_size: '1-10' | '11-50' | '51-200' | '201-500' | '500+' | null;
  approval_status: 'pending' | 'approved' | 'rejected';
  verification_status: 'pending' | 'verified' | 'rejected';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Document {
  id: string;
  user_id: string;
  document_type: string;
  file_name: string;
  file_url: string | null;
  status: 'pending' | 'uploaded' | 'verified' | 'rejected';
  created_at: string;
}

export interface User {
  id: string;
  email?: string;
  user_type?: 'individual' | 'corporate';
  created_at?: string;
} 