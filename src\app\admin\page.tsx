"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  getAllIndividualAccounts,
  getAllCorporateAccounts,
  getAllDocuments,
} from "@/lib/auth-supabase";

interface Account {
  verification_status?: 'pending' | 'verified' | 'rejected';
  approval_status?: 'pending' | 'approved' | 'rejected';
}

interface Document {
  status: 'pending' | 'uploaded' | 'verified' | 'rejected';
}

export default function AdminDashboardPage() {
  const [individualAccounts, setIndividualAccounts] = useState<Account[]>([]);
  const [corporateAccounts, setCorporateAccounts] = useState<Account[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [individualResult, corporateResult, documentsResult] =
        await Promise.all([
          getAllIndividualAccounts(),
          getAllCorporateAccounts(),
          getAllDocuments(),
        ]);

      if (individualResult.success && individualResult.accounts) {
        setIndividualAccounts(individualResult.accounts);
      }
      if (corporateResult.success && corporateResult.accounts) {
        setCorporateAccounts(corporateResult.accounts);
      }
      if (documentsResult.success && documentsResult.documents) {
        setDocuments(documentsResult.documents);
      }
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">Loading Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="mt-2 text-gray-600">
          Overview of accounts and activities.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Individual Accounts</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{individualAccounts.length}</div>
                <p className="text-xs text-muted-foreground">{individualAccounts.filter(acc => acc.verification_status === 'verified').length} verified</p>
            </CardContent>
        </Card>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Corporate Accounts</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{corporateAccounts.length}</div>
                <p className="text-xs text-muted-foreground">{corporateAccounts.filter(acc => acc.approval_status === 'approved').length} approved</p>
            </CardContent>
        </Card>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Documents</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{documents.length}</div>
                <p className="text-xs text-muted-foreground">{documents.filter(doc => doc.status === 'verified').length} verified</p>
            </CardContent>
        </Card>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">
                    {individualAccounts.filter(acc => acc.verification_status === 'pending').length +
                    corporateAccounts.filter(acc => acc.approval_status === 'pending').length}
                </div>
                <p className="text-xs text-muted-foreground">Accounts needing attention</p>
            </CardContent>
        </Card>
      </div>
    </div>
  );
} 