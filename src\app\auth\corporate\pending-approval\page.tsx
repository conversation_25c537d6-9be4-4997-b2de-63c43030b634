import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function PendingApprovalPage() {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <main className="flex flex-1 items-center justify-center pt-20">
        <div className="w-full max-w-2xl bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="mb-6">
            <svg
              className="mx-auto h-16 w-16 text-[#1A0505]"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h1 className="text-2xl font-bold mb-4">Application Submitted Successfully</h1>
          <p className="text-gray-600 mb-8">
            Thank you for submitting your corporate account application. Our team will review your documents and get back to you within 2-3 business days.
          </p>
          <div className="space-y-4">
            <p className="text-sm text-gray-500">
              We will notify you at the email address you provided once your account is approved.
            </p>
            <Link href="/">
              <Button variant="outline">Return to Home</Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
} 