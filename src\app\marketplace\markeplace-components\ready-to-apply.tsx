
import React from 'react';
import { Button } from "@/components/ui/button"
import { MoveRight } from 'lucide-react'

const ReadyToApply = () => {
    return (
        <>
        <div style={{backgroundImage: "url('/background.png')",
                    width:'1585px',
                    height:'879px',
                    top:'2942px',
                    left:'64px',}}
                    >
            <div style={{flexFlow:'horizontal',
                        width:'fixed(1566px)',
                        height:'fixed(320px)',
                        top:'26px',
                        left:'10px',
                        gap:'48px',
                        borderRadius:'20px',
                        backgroundColor:'#FFFFFF',
                        filter: 'drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25))',                        

                    }}>
                    <div style={{width:'1012px',height:'152px',top:'84px',left:'83px',fontFamily:'Inter',fontWeight:'700',fontSize:'64px',lineHeight:'100%',color:'#230B0B',wordSpacing:'0%'}}><p>Ready to apply?</p>
                    <span style={{fontWeight:'400',fontSize:'32px',lineHeight:'100%',color:'#230B0B',wordSpacing:'0%'}}>Create an account to compare and apply for the best loan offers</span></div>
                    <div>
                        <Button variant="outline" style={{
                        width:'fixed(340px)',flexFlow:'horizontal',height:'fixed(75px)',
                        top:'122.5px',left:'1143px',borderRadius:'20px',
                        border:'1px solid #8D938CB2',color:'#0B0A0A',gap:'10px',
                        padding:'10px',


                    }}>
                        <MoveRight fill='white' width={55} height={34} />
                        <span style={{width:'223px',height:'44px',fontFamily:'Inter',fontWeight:'700',fontSize:'36px',lineHeight:'100%',wordSpacing:'0%',color:'#FFFFFF'}}>Sign Up Now</span>
                        </Button>
                    </div>
                    {/* Quick Links and Follow Us */}
                    <div>
                        <div></div>
                        <div></div>
                    </div>
            </div>

        </div>
        </>
    );
}

export default ReadyToApply;
