"use client";

import React from 'react';
import { CorporateAccount } from '@/lib/types';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface Props {
  account: CorporateAccount | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function ViewCorporateAccountModal({ account, isOpen, onClose }: Props) {
  if (!account) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>View Corporate Account</DialogTitle>
        </DialogHeader>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium">Organization Details</h3>
            <div className="text-sm mt-2 space-y-1">
              <p><strong>Organization Name:</strong> {account.organization_name}</p>
              <p><strong>Email:</strong> {account.email}</p>
              <p><strong>Office Address:</strong> {account.office_address}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium">Contact Person</h3>
            <div className="text-sm mt-2 space-y-1">
              <p><strong>Name:</strong> {account.contact_person}</p>
              <p><strong>Phone:</strong> {account.contact_phone}</p>
            </div>
          </div>
          <div>
            <h3 className="font-medium">Business Information</h3>
            <div className="text-sm mt-2 space-y-1">
              <p><strong>Business Type:</strong> {account.business_type}</p>
              <p><strong>Industry:</strong> {account.industry}</p>
              <p><strong>Company Size:</strong> {account.company_size}</p>
            </div>
          </div>
           <div>
            <h3 className="font-medium">Status</h3>
            <div className="text-sm mt-2 space-y-1">
                <p><strong>Approval Status:</strong> {account.approval_status}</p>
                <p><strong>Verification Status:</strong> {account.verification_status}</p>
                <p><strong>Account Status:</strong> {account.is_active ? 'Active' : 'Disabled'}</p>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 