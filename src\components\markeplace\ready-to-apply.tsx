
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { MoveRight } from 'lucide-react';

const ReadyToApply = () => {
    return (
        <div style={{paddingLeft: '64px',paddingRight: '64px'}}>
        <div style={{backgroundImage: "url('/background.png')",width:"full" }}>
            <div className="max-w-[1400px] mx-auto px-4 md:px-8 pt-[26px]">
                {/* Main Card */}
                <div className="bg-white rounded-3xl shadow-sm p-8 md:p-12 flex flex-col md:flex-row justify-between items-center gap-8">
                    {/* Text Content */}
                    <div>
                        <h2 className="text-3xl md:text-[40px] font-bold text-[#230B0B] mb-2">Ready to apply?</h2>
                        <p className="text-lg text-[#230B0B]">
                            Create an account to compare and apply for the best loan offers
                        </p>
                    </div>
                    
                    {/* Sign Up Button */}
                    <Button 
                        className="bg-[#1a1a1a] text-white rounded-20 px-7 py-6 h-auto flex items-center gap-2 min-w-[180px] hover:bg-[#333]"
                    >
                        <MoveRight className="w-5 h-5" />
                        <span className="font-semibold text-lg">Sign Up Now</span>
                    </Button>
                </div>

                {/* Footer Section */}
                <div className="py-16 grid grid-cols-1 md:grid-cols-3 gap-12">
                    {/* Kredxa Column */}
                    <div>
                        <h3 className="text-2xl font-bold mb-4 text-[#230B0B]">Kredxa</h3>
                        <p className="text-gray-700 text-lg leading-relaxed">
                            The modern loan marketplace<br />
                            for individuals & SMEs.<br />
                            Compare, apply and manage<br />
                            loans with confidence.
                        </p>
                    </div>

                    {/* Quick Links Column */}
                    <div>
                        <h3 className="text-2xl font-bold mb-4 text-[#230B0B]">Quick Links</h3>
                        <ul className="space-y-3">
                            <li className="flex items-center gap-2">
                                <span className="w-1.5 h-1.5 rounded-full bg-black"></span>
                                <a href="#" className="text-gray-700 hover:text-black text-lg">About</a>
                            </li>
                            <li className="flex items-center gap-2">
                                <span className="w-1.5 h-1.5 rounded-full bg-black"></span>
                                <a href="#" className="text-gray-700 hover:text-black text-lg">Terms & Conditions</a>
                            </li>
                            <li className="flex items-center gap-2">
                                <span className="w-1.5 h-1.5 rounded-full bg-black"></span>
                                <a href="#" className="text-gray-700 hover:text-black text-lg">Privacy Policy</a>
                            </li>
                            <li className="flex items-center gap-2">
                                <span className="w-1.5 h-1.5 rounded-full bg-black"></span>
                                <a href="#" className="text-gray-700 hover:text-black text-lg">Contact Us</a>
                            </li>
                        </ul>
                    </div>

                    {/* Follow Us Column */}
                    <div>
                        <h3 className="text-2xl font-bold mb-4 text-[#230B0B]">Follow Us</h3>
                        <div className="flex gap-6">
                            <a href="#" className="text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
                                    <path d="M23.643 4.937c-.835.37-1.732.62-2.675.733.962-.576 1.7-1.49 2.048-2.578-.9.534-1.897.922-2.958 1.13-.85-.904-2.06-1.47-3.4-1.47-2.572 0-4.658 2.086-4.658 4.66 0 .364.042.718.12 1.06-3.873-.195-7.304-2.05-9.602-4.868-.4.69-.63 1.49-.63 2.342 0 1.616.823 3.043 2.072 3.878-.764-.025-1.482-.234-2.11-.583v.06c0 2.257 1.605 4.14 3.737 4.568-.392.106-.803.162-1.227.162-.3 0-.593-.028-.877-.082.593 1.85 2.313 3.198 4.352 3.234-1.595 1.25-3.604 1.995-5.786 1.995-.376 0-.747-.022-1.112-.065 2.062 1.323 4.51 2.093 7.14 2.093 8.57 0 13.255-7.098 13.255-13.254 0-.2-.005-.402-.014-.602.91-.658 1.7-1.477 2.323-2.41z"></path>
                                </svg>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
                                    <path d="M22.675 0h-21.35c-.732 0-1.325.593-1.325 1.325v21.351c0 .731.593 1.324 1.325 1.324h11.495v-9.294h-3.128v-3.622h3.128v-2.671c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.795.143v3.24l-1.918.001c-1.504 0-1.795.715-1.795 1.763v2.313h3.587l-.467 3.622h-3.12v9.293h6.116c.73 0 1.323-.593 1.323-1.325v-21.35c0-.732-.593-1.325-1.325-1.325z"></path>
                                </svg>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
                                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
                                </svg>
                            </a>
                            <a href="#" className="text-gray-400 hover:text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="currentColor" className="w-7 h-7">
                                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    );
}

export default ReadyToApply;
