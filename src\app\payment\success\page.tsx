"use client";

import React, { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Loader2, XCircle, ArrowLeft, Download, Eye } from 'lucide-react';
import { verifyPaystackPayment, updatePaymentStatusByReference } from '@/lib/payments';
import { toast } from 'sonner';

function PaymentSuccessContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isVerifying, setIsVerifying] = useState(true);
  const [paymentStatus, setPaymentStatus] = useState<'success' | 'failed' | 'pending'>('pending');
  const [paymentDetails, setPaymentDetails] = useState<{
    amount: number;
    transaction_date: string;
    reference: string;
    status: string;
    gateway_response: string;
    authorization?: {
      card_type: string;
      bank: string;
      last4: string;
    };
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const reference = searchParams.get('reference');
  const trxref = searchParams.get('trxref');

  useEffect(() => {
    const paymentRef = reference || trxref;
    if (paymentRef) {
      verifyPayment(paymentRef);
    } else {
      setError('Payment reference not found');
      setIsVerifying(false);
      setPaymentStatus('failed');
    }
  }, [reference, trxref]);

  const verifyPayment = async (paymentReference: string) => {
    try {
      setIsVerifying(true);
      
      // Verify payment with Paystack
      const result = await verifyPaystackPayment(paymentReference);
      
      if (result.status && result.data) {
        const { data } = result;
        
        if (data.status === 'success') {
          setPaymentStatus('success');
          setPaymentDetails(data);
          
          // Update payment status in database
          const updateResult = await updatePaymentStatusByReference(
            paymentReference,
            'completed',
            data
          );
          
          if (updateResult.success) {
            toast.success('Payment verified and updated successfully!');
          } else {
            console.error('Failed to update payment status:', updateResult.error);
            toast.warning('Payment verified but status update failed. Please contact support.');
          }
          
        } else {
          setPaymentStatus('failed');
          setError(data.gateway_response || 'Payment was not successful');
          
          // Update payment status in database as failed
          const updateResult = await updatePaymentStatusByReference(
            paymentReference,
            'failed',
            data
          );
          
          if (!updateResult.success) {
            console.error('Failed to update payment status:', updateResult.error);
          }
          
          toast.error('Payment verification failed');
        }
      } else {
        setPaymentStatus('failed');
        setError(result.message || 'Payment verification failed');
        toast.error('Payment verification failed');
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      setPaymentStatus('failed');
      setError('Unable to verify payment. Please contact support.');
      toast.error('Payment verification failed');
    } finally {
      setIsVerifying(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount / 100); // Convert from kobo to naira
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-NG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isVerifying) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Loader2 className="w-16 h-16 text-blue-500 mx-auto mb-4 animate-spin" />
            <h2 className="text-xl font-semibold mb-2">Verifying Payment</h2>
            <p className="text-gray-600">
              Please wait while we verify your payment...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (paymentStatus === 'failed') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2 text-red-600">Payment Failed</h2>
            <p className="text-gray-600 mb-4">
              {error || 'Your payment could not be processed.'}
            </p>
            <div className="space-y-3">
              <Button 
                onClick={() => router.push('/dashboard/borrower/my-loans')}
                className="w-full"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to My Loans
              </Button>
              <Button 
                variant="outline" 
                onClick={() => router.push('/dashboard/support')}
                className="w-full"
              >
                Contact Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center pb-4">
          <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <CardTitle className="text-2xl text-green-600">Payment Successful!</CardTitle>
          <p className="text-gray-600">
            Your loan payment has been processed successfully.
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Payment Details */}
          {paymentDetails && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold mb-3 text-green-800">Payment Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-green-600">Amount Paid</p>
                  <p className="font-semibold text-green-800 text-lg">
                    {formatCurrency(paymentDetails.amount)}
                  </p>
                </div>
                <div>
                  <p className="text-green-600">Payment Date</p>
                  <p className="font-semibold text-green-800">
                    {formatDate(paymentDetails.transaction_date)}
                  </p>
                </div>
                <div>
                  <p className="text-green-600">Reference</p>
                  <p className="font-semibold text-green-800 font-mono text-xs">
                    {paymentDetails.reference}
                  </p>
                </div>
                <div>
                  <p className="text-green-600">Status</p>
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Successful
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Payment Method Info */}
          {paymentDetails?.authorization && (
            <div className="border rounded-lg p-4">
              <h3 className="font-semibold mb-3">Payment Method</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Card Type</p>
                  <p className="font-semibold capitalize">
                    {paymentDetails.authorization.card_type}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Bank</p>
                  <p className="font-semibold">
                    {paymentDetails.authorization.bank}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Card Number</p>
                  <p className="font-semibold">
                    **** **** **** {paymentDetails.authorization.last4}
                  </p>
                </div>
                <div>
                  <p className="text-gray-600">Gateway Response</p>
                  <p className="font-semibold text-green-600">
                    {paymentDetails.gateway_response}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold mb-3 text-blue-800">What&apos;s Next?</h3>
            <ul className="space-y-2 text-sm text-blue-700">
              <li>• Your payment will reflect in your loan account within 24 hours</li>
              <li>• You&apos;ll receive an email receipt shortly</li>
              <li>• Your loan balance and payment schedule will be updated</li>
              <li>• You can view your payment history in the My Loans section</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button 
              onClick={() => router.push('/dashboard/borrower/my-loans')}
              className="flex-1"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to My Loans
            </Button>
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard/borrower/my-loans')}
              className="flex-1"
            >
              <Eye className="w-4 h-4 mr-2" />
              View Payment History
            </Button>
            <Button 
              variant="outline" 
              onClick={() => window.print()}
              className="flex-1"
            >
              <Download className="w-4 h-4 mr-2" />
              Print Receipt
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function PaymentSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <Loader2 className="w-16 h-16 text-blue-500 mx-auto mb-4 animate-spin" />
            <h2 className="text-xl font-semibold mb-2">Loading Payment Details</h2>
            <p className="text-gray-600">
              Please wait while we load your payment information...
            </p>
          </CardContent>
        </Card>
      </div>
    }>
      <PaymentSuccessContent />
    </Suspense>
  );
}