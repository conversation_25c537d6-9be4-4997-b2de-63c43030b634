"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Check } from "lucide-react"


export default function ReviewAndConfirm() {
  const [isConfirmed, setIsConfirmed] = useState(false)

  const handleBackClick = () => {
    // Scroll to the define loan section
    const defineLoanSection = document.getElementById('define-loan-section')
    if (defineLoanSection) {
      defineLoanSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div id="review-and-confirm" className="max-w-2xl mx-auto p-6 bg-gray-50">
      <Card className="bg-white shadow-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <Check className="w-5 h-5 text-white" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">Preview & Confirm</CardTitle>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Loan Offer Summary */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Loan Offer Summary</h3>
            <div className="grid grid-cols-3 gap-6">
              <div>
                <p className="text-sm text-gray-600 mb-1">Amount Range:</p>
                <p className="font-medium text-gray-900">₦100,000 - ₦2,000,000</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">Duration:</p>
                <p className="font-medium text-gray-900">1 - 6 months</p>
              </div>
              <div>
                <p className="text-sm text-gray-600 mb-1">Interest Rate:</p>
                <p className="font-medium text-gray-900">5.5% monthly</p>
              </div>
            </div>
          </div>

          {/* Platform Terms */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <h4 className="font-medium text-gray-900 mb-3">Platform Terms (Read-Only)</h4>
            <ul className="space-y-2 text-sm text-gray-700">
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Monthly repayment schedule</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>3-day grace period for late payments</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>2% penalty on overdue amounts</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Kredxa dispute resolution process</span>
              </li>
            </ul>
          </div>

          {/* Confirmation Checkbox */}
          <div className="flex items-start gap-3">
            <Checkbox
              id="confirm"
              checked={isConfirmed}
              onCheckedChange={(checked) => setIsConfirmed(checked as boolean)}
              className="mt-0.5"
            />
            <label htmlFor="confirm" className="text-sm text-gray-700 leading-relaxed cursor-pointer">
              I confirm that the information provided is accurate and complies with Kredxa policy.
            </label>
          </div>

          {/* Action Buttons - Swapped positions */}
          <div className="flex gap-3 pt-4">
            <Button variant="outline" className="px-6" onClick={handleBackClick}>
              Back
            </Button>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white px-6" disabled={!isConfirmed}>
              Publish Loan Offer
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
