# Kredxa - Modern Loan Marketplace

A comprehensive loan marketplace platform built with Next.js, TypeScript, and Supabase, designed for both individual borrowers and corporate lenders.

## Features

### 🔐 Authentication & User Management
- **Individual & Corporate Accounts**: Separate signup and login flows for different user types
- **Email Verification**: Secure email-based account verification
- **Password Reset**: Forgot password functionality with email reset links
- **Session Management**: Persistent authentication with Supabase Auth
- **Role-based Access**: Different dashboards for borrowers and lenders

### 💰 Borrower Features
- **Marketplace**: Browse and compare loan offers from multiple lenders
- **Loan Applications**: Apply for loans with detailed forms
- **Loan Management**: Track active loans and payment history
- **Document Management**: Upload and manage required documents
- **KYC Integration**: Complete Know Your Customer verification process

### 🏦 Lender Features
- **Loan Offer Creation**: Create and manage loan offers with flexible terms
- **Portfolio Management**: Track all active loans and borrower information
- **Reports & Analytics**: Comprehensive earnings and performance reports
- **Document Verification**: Review and approve borrower documents
- **Risk Assessment**: Built-in tools for evaluating borrower risk

### 📊 Dashboard & Analytics
- **Real-time Metrics**: Live updates on loans, earnings, and performance
- **Interactive Charts**: Visual representation of financial data
- **Export Capabilities**: Download reports in various formats
- **Notifications**: Real-time alerts for important events

## Tech Stack

- **Frontend**: Next.js 15, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **UI Components**: Custom component library with shadcn/ui
- **Authentication**: Supabase Auth with Row Level Security
- **Database**: PostgreSQL with comprehensive schema
- **File Storage**: Supabase Storage for document management

## Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- Git

## Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd kredxa-web
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Set Up Supabase

#### Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new account
2. Create a new project
3. Note down your project URL and anon key

#### Configure Environment Variables

1. Copy the example environment file:
```bash
cp env.example .env.local
```

2. Update `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

#### Run Database Migrations

1. Install Supabase CLI (optional but recommended):
```bash
npm install -g supabase
```

2. Link your project:
```bash
supabase link --project-ref your-project-id
```

3. Run the initial migration:
```bash
supabase db push
```

Alternatively, you can run the migration manually in the Supabase SQL editor:

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `supabase/migrations/001_initial_schema.sql`
4. Execute the migration

### 4. Start the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Database Schema

### Core Tables

#### `profiles`
- User profile information
- Links to Supabase Auth users
- User type (individual/corporate)

#### `individual_accounts`
- Detailed individual user information
- Personal details, employment, guarantor info
- Financial information

#### `corporate_accounts`
- Corporate entity information
- Company details, financial data
- Business verification information

#### `documents`
- Document management system
- File uploads and verification status
- Categorized by borrowing/lending requirements

### Key Features

- **Row Level Security (RLS)**: All tables have RLS enabled for data protection
- **Automatic Timestamps**: Created/updated timestamps on all records
- **Foreign Key Relationships**: Proper referential integrity
- **Indexes**: Optimized for common query patterns

## Authentication Flow

### Individual Users
1. Sign up with email/password
2. Email verification required
3. Complete KYC process
4. Access borrower dashboard

### Corporate Users
1. Sign up with corporate email
2. Email verification required
3. Upload business documents
4. Access lender dashboard

### Password Reset
1. Request reset via email
2. Click reset link in email
3. Set new password
4. Automatic redirect to login

## API Structure

### Authentication Endpoints
- `POST /auth/signup` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/reset-password` - Password reset

### User Management
- `GET /api/profile` - Get user profile
- `PUT /api/profile` - Update user profile
- `GET /api/account` - Get account details
- `PUT /api/account` - Update account details

### Document Management
- `GET /api/documents` - List user documents
- `POST /api/documents` - Upload document
- `PUT /api/documents/:id` - Update document status

## Development

### Project Structure

```
kredxa-web/
├── src/
│   ├── app/                    # Next.js app router
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # Dashboard pages
│   │   └── ...
│   ├── components/            # Reusable components
│   │   ├── ui/               # Base UI components
│   │   ├── dashboard/        # Dashboard components
│   │   └── ...
│   ├── lib/                  # Utility functions
│   │   ├── supabase.ts       # Supabase client
│   │   ├── auth-supabase.ts  # Auth functions
│   │   └── utils.ts          # General utilities
│   └── data/                 # Mock data and types
├── supabase/
│   └── migrations/           # Database migrations
├── public/                   # Static assets
└── ...
```

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

### Code Style

- **TypeScript**: Strict mode enabled
- **ESLint**: Configured with Next.js rules
- **Prettier**: Code formatting
- **Conventional Commits**: Git commit message format

## Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms

The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Security Features

- **Row Level Security**: Database-level access control
- **JWT Authentication**: Secure token-based auth
- **Email Verification**: Required for all accounts
- **Password Requirements**: Minimum 6 characters
- **HTTPS Only**: Secure connections in production
- **CORS Protection**: Configured for security

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For support and questions:
- Create an issue in the GitHub repository
- Check the documentation in the `/docs` folder
- Review the Supabase documentation

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Powered by [Supabase](https://supabase.com/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
