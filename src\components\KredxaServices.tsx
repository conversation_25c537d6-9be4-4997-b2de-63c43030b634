import React from 'react';
import Image from 'next/image';
import { Card } from './ui/card';

const KredxaServices = () => {
  return (
    <div
      className="py-12 sm:py-16"
      style={{
        backgroundImage: "url('/image 18.svg')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <div className="text-center px-4">
        <h3 className="text-base sm:text-lg font-semibold text-gray-600">Check Out Some Facts About</h3>
        <h2 className="text-3xl sm:text-4xl font-bold mt-2">Kredxa Services</h2>
        <p className="text-gray-500 mt-4 text-sm sm:text-base">We have various features that distinct us from others</p>
      </div>
      <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto px-4">
        <Card className="border border-gray-200 rounded-none shadow-1x p-6 flex items-center justify-center space-x-4">
          <Image src="/image 11.svg" alt="Loans" width={50} height={50} />
          <div>
            <p className="text-2xl font-bold">₦840k</p>
            <p className="text-sm text-gray-500">LOANS</p>
          </div>
        </Card>
        <Card className="border border-gray-200 rounded-none shadow-1x p-6 flex items-center justify-center space-x-4">
          <Image src="/image.svg" alt="Reserves" width={50} height={50} />
          <div>
            <p className="text-2xl font-bold">₦2.42M</p>
            <p className="text-sm text-gray-500">RESERVES</p>
          </div>
        </Card>
        <Card className="border border-gray-200 rounded-none shadow-1x p-6 flex items-center justify-center space-x-4">
          <Image src="/image (1).svg" alt="Orders" width={50} height={50} />
          <div>
            <p className="text-2xl font-bold">₦5K</p>
            <p className="text-sm text-gray-500">ORDERS</p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default KredxaServices;