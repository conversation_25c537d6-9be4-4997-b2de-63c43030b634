-- Add foreign key relationships between profiles and account tables
-- Since both reference auth.users(id), we can establish relationships through user_id = profiles.id

-- First, clean up any orphaned records (shouldn't be any in a new system)
DELETE FROM individual_accounts 
WHERE user_id NOT IN (SELECT id FROM profiles);

DELETE FROM corporate_accounts 
WHERE user_id NOT IN (SELECT id FROM profiles);

-- Add foreign key constraints
ALTER TABLE individual_accounts 
ADD CONSTRAINT fk_individual_accounts_profile 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

ALTER TABLE corporate_accounts 
ADD CONSTRAINT fk_corporate_accounts_profile 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Add indexes for better performance on joins
CREATE INDEX IF NOT EXISTS idx_individual_accounts_profile ON individual_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_corporate_accounts_profile ON corporate_accounts(user_id);

-- Update RLS policies to work with the new relationships
-- Allow public read access to account info for loan offers (with proper joins)
CREATE POLICY "Allow profile joins to individual accounts" ON individual_accounts 
FOR SELECT USING (true);

CREATE POLICY "Allow profile joins to corporate accounts" ON corporate_accounts 
FOR SELECT USING (true); 