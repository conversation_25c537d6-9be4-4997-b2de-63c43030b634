-- Add admin role functionality
-- Update profiles table to include admin role
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS admin_level TEXT CHECK (admin_level IN ('super_admin', 'admin', 'moderator')) DEFAULT NULL;

-- Create admin_users table for additional admin information
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  email TEXT NOT NULL,
  admin_level TEXT CHECK (admin_level IN ('super_admin', 'admin', 'moderator')) NOT NULL,
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for admin functionality
CREATE INDEX IF NOT EXISTS idx_profiles_is_admin ON profiles(is_admin);
CREATE INDEX IF NOT EXISTS idx_profiles_admin_level ON profiles(admin_level);
CREATE INDEX IF NOT EXISTS idx_admin_users_user_id ON admin_users(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_admin_level ON admin_users(admin_level);
CREATE INDEX IF NOT EXISTS idx_admin_users_is_active ON admin_users(is_active);

-- Create trigger for admin_users updated_at
CREATE TRIGGER update_admin_users_updated_at 
  BEFORE UPDATE ON admin_users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on admin_users table
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;

-- Admin users policies (only admins can access admin data)
CREATE POLICY "Admins can view admin users" ON admin_users FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.is_admin = TRUE
  ));

CREATE POLICY "Admins can update admin users" ON admin_users FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.is_admin = TRUE
  ));

CREATE POLICY "Admins can insert admin users" ON admin_users FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.is_admin = TRUE
  ));

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = user_uuid 
    AND is_admin = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get admin level
CREATE OR REPLACE FUNCTION get_admin_level(user_uuid UUID DEFAULT auth.uid())
RETURNS TEXT AS $$
BEGIN
  RETURN (
    SELECT admin_level FROM profiles 
    WHERE id = user_uuid 
    AND is_admin = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create admin user
CREATE OR REPLACE FUNCTION create_admin_user(
  admin_email TEXT,
  admin_level TEXT DEFAULT 'admin',
  permissions JSONB DEFAULT '{}'
)
RETURNS BOOLEAN AS $$
DECLARE
  user_uuid UUID;
BEGIN
  -- Check if current user is super admin
  IF NOT (SELECT is_admin() AND get_admin_level() = 'super_admin') THEN
    RAISE EXCEPTION 'Only super admins can create admin users';
  END IF;

  -- Find user by email
  SELECT id INTO user_uuid FROM auth.users WHERE email = admin_email;
  
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', admin_email;
  END IF;

  -- Update profile to make user admin
  UPDATE profiles 
  SET is_admin = TRUE, admin_level = admin_level
  WHERE id = user_uuid;

  -- Insert into admin_users table
  INSERT INTO admin_users (user_id, email, admin_level, permissions, created_by)
  VALUES (user_uuid, admin_email, admin_level, permissions, auth.uid());

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to remove admin privileges
CREATE OR REPLACE FUNCTION remove_admin_user(admin_email TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_uuid UUID;
BEGIN
  -- Check if current user is super admin
  IF NOT (SELECT is_admin() AND get_admin_level() = 'super_admin') THEN
    RAISE EXCEPTION 'Only super admins can remove admin users';
  END IF;

  -- Find user by email
  SELECT id INTO user_uuid FROM auth.users WHERE email = admin_email;
  
  IF user_uuid IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', admin_email;
  END IF;

  -- Remove admin privileges
  UPDATE profiles 
  SET is_admin = FALSE, admin_level = NULL
  WHERE id = user_uuid;

  -- Deactivate in admin_users table
  UPDATE admin_users 
  SET is_active = FALSE
  WHERE user_id = user_uuid;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert a default super admin (you'll need to replace with actual user ID)
-- This should be run manually after creating your first user
-- Example: SELECT create_admin_user('<EMAIL>', 'super_admin'); 