-- Drop existing admin policies
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all tickets" ON support_tickets;
DROP POLICY IF EXISTS "Ad<PERSON> can view all messages" ON support_messages;

-- Recreate admin policies with better logic
-- <PERSON><PERSON> can view all tickets (check both admin_users and profiles with is_admin flag)
CREATE POLICY "Ad<PERSON> can view all tickets" ON support_tickets
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = true
    )
  );

-- Ad<PERSON> can view all messages (check both admin_users and profiles with is_admin flag)
CREATE POLICY "Ad<PERSON> can view all messages" ON support_messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() AND is_active = true
    )
    OR
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND is_admin = true
    )
  ); 