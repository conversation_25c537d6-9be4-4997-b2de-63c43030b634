"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import { Input } from './ui/input';
import { Button } from './ui/button';

const Newsletter = () => {
  const [subscribed, setSubscribed] = useState(false);

  const handleSubscribe = () => {
    setSubscribed(true);
  };

  return (
    <div className="bg-white py-12 sm:py-16">
      <div className="max-w-4xl mx-auto px-4">
        <div className="border rounded-lg p-4 sm:p-8 flex flex-col md:flex-row items-center text-center md:text-left">
          <div className="w-full md:w-1/3">
            <Image src="/image 21.svg" alt="Newsletter" width={200} height={200} className="mx-auto" />
          </div>
          <div className="w-full md:w-2/3 mt-8 md:mt-0 md:pl-8">
            <h3 className="text-base sm:text-lg font-semibold text-gray-600">SUBSCRIBE TO KREDXA</h3>
            <h2 className="text-2xl sm:text-3xl font-bold mt-2">To Get Exclusive Benefits</h2>
            <div className="mt-6 flex flex-col sm:flex-row">
              <Input type="email" placeholder="Your Email Address" className="rounded-full sm:rounded-l-full sm:rounded-r-none mb-2 sm:mb-0" />
              <Button
                className="bg-black text-white rounded-full sm:rounded-r-full sm:rounded-l-none"
                onClick={handleSubscribe}
              >
                {subscribed ? 'SUBSCRIBED!' : 'SUBSCRIBE'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Newsletter;